# ✏️ Custom Room Name Feature Guide

## New Feature: Add Your Own Room Names

Users can now **type their own custom room names** if the room they need isn't in the dropdown list! This makes the system completely flexible for any room naming scenario.

## 🎯 How It Works

### **Two Input Modes:**

#### 1. **📋 Search Mode** (Default)
- Search through existing room names from CSV
- Select from filtered dropdown list
- Standard workflow for known rooms

#### 2. **✏️ Custom Mode** (New!)
- Type any custom room name you want
- No restrictions - use any naming convention
- Perfect for new rooms, temporary spaces, or different naming systems

## 🎮 User Interface

### **Mode Toggle Buttons**
```
[📋 Search] [✏️ Custom]
```
- **Search**: Active by default, shows search input
- **Custom**: Switch to custom input mode

### **Search Mode UI**
```
┌─────────────────────────────────────┐
│ Assign Room Name                    │
│ [📋 Search] [✏️ Custom]            │
│ [Search room names...            ]  │
│ ┌─────────────────────────────────┐ │
│ │ Room 101                        │ │
│ │ Room 102                        │ │
│ │ Room 103                        │ │
│ └─────────────────────────────────┘ │
│ [Cancel] [Select] [✏️ Custom Name] │
└─────────────────────────────────────┘
```

### **Custom Mode UI**
```
┌─────────────────────────────────────┐
│ Assign Room Name                    │
│ [📋 Search] [✏️ Custom]            │
│ [Type custom room name...        ]  │
│ ┌─────────────────────────────────┐ │
│ │    Custom Room: "Meeting Room"  │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
│ [Cancel] [Add Custom Room (Enter)] │
└─────────────────────────────────────┘
```

## 🧪 Test Scenarios

### **Scenario 1: Standard Workflow**
1. Draw annotation box
2. Room dropdown appears in **Search mode**
3. Type "Room" → See filtered results
4. Click "Room 101" → Annotation gets "Room 101"

### **Scenario 2: Custom Room Name**
1. Draw annotation box
2. Click **✏️ Custom** button
3. Type "Executive Conference Room"
4. Press **Enter** or click **Add Custom Room**
5. Annotation gets "Executive Conference Room"

### **Scenario 3: Room Not Found → Switch to Custom**
1. Draw annotation box
2. Search for "Boardroom" → No results found
3. Click **"Type custom name instead"** button
4. Type "Boardroom Alpha"
5. Add custom room → Annotation gets "Boardroom Alpha"

### **Scenario 4: Quick Custom Access**
1. Draw annotation box
2. Click **✏️ Custom Name** button in footer
3. Type "Temporary Storage"
4. Press **Enter** → Annotation gets "Temporary Storage"

## 🎨 Visual Features

### **Mode Indicators**
- **Active Mode**: Green button with white text
- **Inactive Mode**: Gray button with dark text
- **Custom Input**: Green border, light green background

### **Custom Room Preview**
- Shows exactly what will be added: `Custom Room: "Your Text"`
- Real-time preview as you type
- Clear visual feedback

### **Smart Hints**
- **Empty Custom**: "Type your custom room name above and press Enter or click Add"
- **No Search Results**: Shows "Type custom name instead" button
- **Button Tooltips**: Helpful hover text

## 🚀 Use Cases

### **New Buildings/Areas**
- Rooms not yet in the CSV database
- Temporary construction areas
- Future expansion zones

### **Different Naming Conventions**
- "Conference Room A" instead of "Room 101"
- "John's Office" instead of generic room numbers
- "Storage - Level 3" for specific purposes

### **Flexible Workflows**
- Mix of CSV rooms and custom names in same project
- Quick annotation without waiting for CSV updates
- Field work where exact room names vary

### **Special Situations**
- Emergency exits: "Fire Exit - North"
- Utility areas: "Electrical Room - Main"
- Outdoor spaces: "Parking Lot - Section B"

## ⌨️ Keyboard Shortcuts

### **Search Mode**
- **Arrow Keys**: Navigate room list
- **Enter**: Select highlighted room
- **Escape**: Cancel/close dropdown

### **Custom Mode**
- **Type**: Enter custom room name
- **Enter**: Add custom room
- **Escape**: Cancel/close dropdown

## 🎯 Benefits

### **Complete Flexibility**
- No longer limited to CSV room names
- Can handle any room naming scenario
- Perfect for evolving building layouts

### **User-Friendly**
- Clear visual modes (Search vs Custom)
- Intuitive toggle between modes
- Helpful hints and previews

### **Workflow Efficiency**
- Quick access to custom mode
- No need to update CSV for one-off rooms
- Seamless integration with existing features

### **Professional Results**
- Custom names appear in exports
- Consistent with CSV-based room names
- Full annotation functionality maintained

## 🔄 Integration with Hierarchical Filter

### **Filtered + Custom**
- Hierarchical filter still works
- Custom rooms bypass filter restrictions
- Best of both worlds: structure + flexibility

### **Example Workflow**
1. Set filter: "Building A → Floor 1"
2. Draw annotation → See only Floor 1 rooms
3. Need custom room → Switch to Custom mode
4. Type "New Meeting Room" → Add custom room
5. Continue with filtered rooms for other annotations

## 🎉 Success Criteria

✅ **Feature Works If**:
- Can toggle between Search and Custom modes
- Custom input accepts any text
- Custom rooms are added to annotations
- Search mode still works normally
- Keyboard shortcuts work in both modes
- Visual feedback is clear and helpful
- Custom rooms appear in exports
- Works with hierarchical filtering

## 🧪 Quick Test Checklist

1. **Basic Custom**: Draw box → Custom mode → Type "Test Room" → Add ✅
2. **Mode Toggle**: Switch between Search/Custom modes ✅
3. **Search Fallback**: Search fails → "Type custom name instead" ✅
4. **Keyboard**: Enter key works in custom mode ✅
5. **Visual Preview**: See "Custom Room: 'Your Text'" preview ✅
6. **Export**: Custom room names appear in exported data ✅

The custom room name feature provides complete flexibility while maintaining the structured benefits of the hierarchical filter system! ✏️🎯

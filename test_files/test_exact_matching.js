// Test script to demonstrate the new exact matching behavior for PDF search
// This simulates the searchRoomCodesInTextItems function behavior

// Mock text items that might be found in a PDF
const mockTextItems = [
  { text: "08.A.08", x: 100, y: 200, width: 50, height: 12 },
  { text: "08.A.08.1", x: 200, y: 200, width: 60, height: 12 },
  { text: "Room 08.A.08 is here", x: 300, y: 200, width: 120, height: 12 },
  { text: "Storage 08.A.08.1 area", x: 400, y: 200, width: 130, height: 12 },
  { text: "08.A.08.1.2", x: 500, y: 200, width: 70, height: 12 },
  { text: "01.E.28", x: 600, y: 200, width: 50, height: 12 },
  { text: "BIOWASTE 01.E.28 STORAGE", x: 700, y: 200, width: 150, height: 12 }
]

// Mock room codes to search for
const mockRoomCodes = [
  { code: "08.A.08", roomName: "STORAGE 08.A.08" },
  { code: "08.A.08.1", roomName: "ARCHIVE 08.A.08.1" },
  { code: "01.E.28", roomName: "BIOWASTE 01.E.28" }
]

// Simulate the new exact matching logic
const searchRoomCodesInTextItems = (textItems, roomCodes) => {
  const foundCodes = new Map()

  roomCodes.forEach(({ code, roomName }) => {
    console.log(`\nSearching for room code: "${code}"`)
    
    // Search for exact matches of the room code with strict word boundary matching
    const matchingItems = textItems.filter(item => {
      const text = item.text.trim()
      
      // Exact match - the text is exactly the room code
      if (text === code) {
        console.log(`  ✅ Exact match found: "${text}"`)
        return true
      }
      
      // Word boundary match - room code appears as a complete word
      // Use regex that ensures the code is not part of a longer alphanumeric sequence
      const escapedCode = code.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
      // Match the code when it's preceded and followed by non-alphanumeric characters or string boundaries
      const strictBoundaryRegex = new RegExp(`(?:^|[^a-zA-Z0-9.])${escapedCode}(?:[^a-zA-Z0-9.]|$)`, 'g')

      if (strictBoundaryRegex.test(text)) {
        console.log(`  ✅ Strict boundary match found: "${text}"`)
        return true
      }
      
      console.log(`  ❌ No match in: "${text}"`)
      return false
    })

    if (matchingItems.length > 0) {
      const match = matchingItems[0]
      foundCodes.set(code, {
        code: code,
        roomName: roomName,
        coordinates: {
          x: match.x,
          y: match.y,
          width: match.width,
          height: match.height
        },
        textItem: match,
        found: true
      })
      console.log(`  🎯 Found "${code}" at coordinates (${match.x}, ${match.y})`)
    } else {
      console.log(`  ⚠️  Room code "${code}" not found`)
    }
  })

  return foundCodes
}

console.log("Testing Exact Room Code Matching in PDF Text")
console.log("=" .repeat(60))

console.log("\nMock PDF Text Items:")
mockTextItems.forEach((item, index) => {
  console.log(`  ${index + 1}. "${item.text}" at (${item.x}, ${item.y})`)
})

console.log("\nRoom Codes to Search:")
mockRoomCodes.forEach(({ code, roomName }) => {
  console.log(`  - "${code}" from "${roomName}"`)
})

console.log("\n" + "=" .repeat(60))
console.log("SEARCH RESULTS:")

const results = searchRoomCodesInTextItems(mockTextItems, mockRoomCodes)

console.log("\n" + "=" .repeat(60))
console.log("SUMMARY:")
console.log(`Total room codes searched: ${mockRoomCodes.length}`)
console.log(`Room codes found: ${results.size}`)
console.log(`Success rate: ${Math.round((results.size / mockRoomCodes.length) * 100)}%`)

console.log("\nFound room codes:")
results.forEach((data, code) => {
  console.log(`  ✅ ${code} -> "${data.textItem.text}" at (${data.coordinates.x}, ${data.coordinates.y})`)
})

const notFound = mockRoomCodes.filter(({ code }) => !results.has(code))
if (notFound.length > 0) {
  console.log("\nNot found room codes:")
  notFound.forEach(({ code }) => {
    console.log(`  ❌ ${code}`)
  })
}

console.log("\n" + "=" .repeat(60))
console.log("KEY IMPROVEMENTS:")
console.log("1. ✅ Exact matching: '08.A.08' matches only '08.A.08', not '08.A.08.1'")
console.log("2. ✅ Word boundary matching: '08.A.08' matches in 'Room 08.A.08 is here'")
console.log("3. ✅ No partial matches: '08.A.08' does NOT match '08.A.08.1.2'")
console.log("4. ✅ Substring extraction: Room codes extracted after last space without regex validation")

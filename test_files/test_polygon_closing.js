/**
 * Test file for polygon closing functionality
 * Tests the new feature where users can close polygons by clicking on the first point
 */

// Test the isNearFirstPoint function
function testIsNearFirstPoint() {
  console.log('Testing isNearFirstPoint function...');
  
  // Mock polygon points
  const polygonPoints = [
    { x: 100, y: 100 }, // First point
    { x: 200, y: 100 },
    { x: 200, y: 200 },
    { x: 100, y: 200 }
  ];
  
  // Test cases
  const testCases = [
    {
      name: 'Click exactly on first point',
      clickPoint: { x: 100, y: 100 },
      expected: true
    },
    {
      name: 'Click near first point (within threshold)',
      clickPoint: { x: 105, y: 105 },
      expected: true
    },
    {
      name: 'Click on edge of threshold',
      clickPoint: { x: 115, y: 100 },
      expected: true
    },
    {
      name: 'Click outside threshold',
      clickPoint: { x: 120, y: 100 },
      expected: false
    },
    {
      name: 'Click on second point',
      clickPoint: { x: 200, y: 100 },
      expected: false
    },
    {
      name: 'Click far away',
      clickPoint: { x: 300, y: 300 },
      expected: false
    }
  ];
  
  // Mock isNearFirstPoint function (copy of the actual implementation)
  function isNearFirstPoint(clickPoint, polygonPoints, threshold = 15) {
    if (!polygonPoints || polygonPoints.length === 0) return false;
    
    const firstPoint = polygonPoints[0];
    const distance = Math.sqrt(
      Math.pow(clickPoint.x - firstPoint.x, 2) + 
      Math.pow(clickPoint.y - firstPoint.y, 2)
    );
    
    return distance <= threshold;
  }
  
  let passed = 0;
  let failed = 0;
  
  testCases.forEach(testCase => {
    const result = isNearFirstPoint(testCase.clickPoint, polygonPoints);
    if (result === testCase.expected) {
      console.log(`✅ ${testCase.name}: PASSED`);
      passed++;
    } else {
      console.log(`❌ ${testCase.name}: FAILED (expected ${testCase.expected}, got ${result})`);
      failed++;
    }
  });
  
  console.log(`\nTest Results: ${passed} passed, ${failed} failed\n`);
  return failed === 0;
}

// Test polygon closing workflow
function testPolygonClosingWorkflow() {
  console.log('Testing polygon closing workflow...');
  
  // Simulate polygon creation workflow
  let polygonPoints = [];
  let currentAnnotation = null;
  
  // Mock functions
  function addPoint(point) {
    polygonPoints.push(point);
    if (!currentAnnotation) {
      currentAnnotation = {
        id: 'test-polygon',
        type: 'polygon',
        points: [point]
      };
    } else {
      currentAnnotation.points = [...polygonPoints];
    }
  }
  
  function canClosePolygon() {
    return polygonPoints.length >= 3;
  }
  
  function isNearFirstPoint(clickPoint, threshold = 15) {
    if (polygonPoints.length === 0) return false;
    const firstPoint = polygonPoints[0];
    const distance = Math.sqrt(
      Math.pow(clickPoint.x - firstPoint.x, 2) + 
      Math.pow(clickPoint.y - firstPoint.y, 2)
    );
    return distance <= threshold;
  }
  
  // Test workflow
  console.log('1. Adding first point...');
  addPoint({ x: 100, y: 100 });
  console.log(`   Points: ${polygonPoints.length}, Can close: ${canClosePolygon()}`);
  
  console.log('2. Adding second point...');
  addPoint({ x: 200, y: 100 });
  console.log(`   Points: ${polygonPoints.length}, Can close: ${canClosePolygon()}`);
  
  console.log('3. Adding third point...');
  addPoint({ x: 200, y: 200 });
  console.log(`   Points: ${polygonPoints.length}, Can close: ${canClosePolygon()}`);
  
  console.log('4. Testing click near first point...');
  const clickNearFirst = { x: 105, y: 105 };
  const nearFirst = isNearFirstPoint(clickNearFirst);
  console.log(`   Click at (${clickNearFirst.x}, ${clickNearFirst.y}), Near first: ${nearFirst}`);
  
  console.log('5. Testing click away from first point...');
  const clickAway = { x: 300, y: 300 };
  const nearFirstAway = isNearFirstPoint(clickAway);
  console.log(`   Click at (${clickAway.x}, ${clickAway.y}), Near first: ${nearFirstAway}`);
  
  // Verify workflow logic
  const canCloseWithClick = canClosePolygon() && nearFirst;
  const cannotCloseWithAwayClick = !(canClosePolygon() && nearFirstAway);
  
  console.log(`\n✅ Can close with click near first point: ${canCloseWithClick}`);
  console.log(`✅ Cannot close with click away from first point: ${cannotCloseWithAwayClick}`);
  
  return canCloseWithClick && cannotCloseWithAwayClick;
}

// Manual testing instructions
function printManualTestInstructions() {
  console.log('\n=== MANUAL TESTING INSTRUCTIONS ===');
  console.log('1. Open the application in browser');
  console.log('2. Load a PDF file');
  console.log('3. Select "Polygon" drawing mode');
  console.log('4. Click to place first point (should appear GREEN)');
  console.log('5. Click to place second point (should appear BLUE)');
  console.log('6. Click to place third point (should appear BLUE)');
  console.log('7. Hover over the first GREEN point:');
  console.log('   - Point should become larger');
  console.log('   - Border should turn YELLOW');
  console.log('   - Text "Click to close polygon" should appear');
  console.log('8. Click on the first GREEN point:');
  console.log('   - Polygon should close');
  console.log('   - Room dropdown should appear (if CSV loaded)');
  console.log('   - OR polygon should be completed (if no CSV)');
  console.log('9. Test with Enter key as well (existing functionality)');
  console.log('10. Verify both methods work consistently');
  console.log('=====================================\n');
}

// Run all tests
function runAllTests() {
  console.log('🧪 Running Polygon Closing Feature Tests\n');
  
  const test1 = testIsNearFirstPoint();
  const test2 = testPolygonClosingWorkflow();
  
  console.log('=== OVERALL TEST RESULTS ===');
  if (test1 && test2) {
    console.log('🎉 All tests PASSED!');
  } else {
    console.log('❌ Some tests FAILED!');
  }
  
  printManualTestInstructions();
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testPolygonClosing = {
    runAllTests,
    testIsNearFirstPoint,
    testPolygonClosingWorkflow,
    printManualTestInstructions
  };
}

// Run tests if in Node.js environment
if (typeof process !== 'undefined' && process.versions && process.versions.node) {
  runAllTests();
}

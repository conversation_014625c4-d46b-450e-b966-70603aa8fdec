// Test script to verify the Y-coordinate flip fix
// This tests the corrected coordinate conversion between PDF and Canvas

// Mock PDF and Canvas dimensions (based on typical values)
const mockPdfDimensions = {
  width: 612,   // Standard letter size PDF width in points
  height: 792   // Standard letter size PDF height in points
}

const mockCanvasDimensions = {
  width: 1275,  // Canvas width at 150 DPI (612 * 150/72)
  height: 1650  // Canvas height at 150 DPI (792 * 150/72)
}

// Corrected coordinate conversion functions
const pdfToCanvasCoordinates = (pdfX, pdfY) => {
  const scaleX = mockCanvasDimensions.width / mockPdfDimensions.width
  const scaleY = mockCanvasDimensions.height / mockPdfDimensions.height
  
  // PDF coordinates have origin at bottom-left, canvas has origin at top-left
  // So we need to flip the Y coordinate
  const canvasX = pdfX * scaleX
  const canvasY = (mockPdfDimensions.height - pdfY) * scaleY
  
  return {
    x: canvasX,
    y: canvasY
  }
}

const canvasToPdfCoordinates = (canvasX, canvasY) => {
  const scaleX = mockPdfDimensions.width / mockCanvasDimensions.width
  const scaleY = mockPdfDimensions.height / mockCanvasDimensions.height
  
  // Convert back: flip Y coordinate and scale
  const pdfX = canvasX * scaleX
  const pdfY = mockPdfDimensions.height - (canvasY * scaleY)
  
  return {
    x: pdfX,
    y: pdfY
  }
}

console.log("Testing Coordinate Flip Fix:")
console.log("=" .repeat(60))
console.log(`PDF Dimensions: ${mockPdfDimensions.width} x ${mockPdfDimensions.height}`)
console.log(`Canvas Dimensions: ${mockCanvasDimensions.width} x ${mockCanvasDimensions.height}`)
console.log("=" .repeat(60))

// Test room code coordinates (simulating text found in PDF)
// These are in PDF coordinate system (bottom-left origin)
const testRoomCodes = [
  { code: "01.E.24", pdfX: 450, pdfY: 200, description: "Bottom half of PDF" },
  { code: "01.A.15", pdfX: 400, pdfY: 600, description: "Top half of PDF" },
  { code: "01.B.05", pdfX: 400, pdfY: 400, description: "Middle of PDF" },
  { code: "02.A.08", pdfX: 450, pdfY: 700, description: "Near top of PDF" },
  { code: "02.B.03", pdfX: 450, pdfY: 100, description: "Near bottom of PDF" }
]

console.log("Room Code Coordinate Conversions (with Y-flip fix):")
console.log("-" .repeat(60))

testRoomCodes.forEach(({ code, pdfX, pdfY, description }) => {
  const canvasCoords = pdfToCanvasCoordinates(pdfX, pdfY)
  const backToPdf = canvasToPdfCoordinates(canvasCoords.x, canvasCoords.y)
  
  console.log(`${code} (${description}):`)
  console.log(`  PDF:    (${pdfX}, ${pdfY})`)
  console.log(`  Canvas: (${canvasCoords.x.toFixed(2)}, ${canvasCoords.y.toFixed(2)})`)
  console.log(`  Back:   (${backToPdf.x.toFixed(2)}, ${backToPdf.y.toFixed(2)})`)
  
  // Check if conversion is reversible (should be very close to original)
  const xDiff = Math.abs(pdfX - backToPdf.x)
  const yDiff = Math.abs(pdfY - backToPdf.y)
  const isAccurate = xDiff < 0.01 && yDiff < 0.01
  
  console.log(`  Accuracy: ${isAccurate ? '✅ PASS' : '❌ FAIL'} (diff: ${xDiff.toFixed(4)}, ${yDiff.toFixed(4)})`)
  console.log("")
})

// Test edge cases with Y-flip
console.log("Edge Case Tests (Y-flip verification):")
console.log("-" .repeat(60))

const edgeCases = [
  { name: "PDF Bottom-left (0, 0)", pdfX: 0, pdfY: 0, expectedCanvasY: mockCanvasDimensions.height },
  { name: "PDF Top-left (0, 792)", pdfX: 0, pdfY: mockPdfDimensions.height, expectedCanvasY: 0 },
  { name: "PDF Bottom-right (612, 0)", pdfX: mockPdfDimensions.width, pdfY: 0, expectedCanvasY: mockCanvasDimensions.height },
  { name: "PDF Top-right (612, 792)", pdfX: mockPdfDimensions.width, pdfY: mockPdfDimensions.height, expectedCanvasY: 0 },
  { name: "PDF Center (306, 396)", pdfX: mockPdfDimensions.width / 2, pdfY: mockPdfDimensions.height / 2, expectedCanvasY: mockCanvasDimensions.height / 2 }
]

edgeCases.forEach(({ name, pdfX, pdfY, expectedCanvasY }) => {
  const canvasCoords = pdfToCanvasCoordinates(pdfX, pdfY)
  const backToPdf = canvasToPdfCoordinates(canvasCoords.x, canvasCoords.y)
  
  console.log(`${name}:`)
  console.log(`  PDF -> Canvas: (${pdfX}, ${pdfY}) -> (${canvasCoords.x.toFixed(2)}, ${canvasCoords.y.toFixed(2)})`)
  console.log(`  Expected Canvas Y: ${expectedCanvasY}`)
  console.log(`  Canvas -> PDF: (${canvasCoords.x.toFixed(2)}, ${canvasCoords.y.toFixed(2)}) -> (${backToPdf.x.toFixed(2)}, ${backToPdf.y.toFixed(2)})`)
  
  const yFlipCorrect = Math.abs(canvasCoords.y - expectedCanvasY) < 0.01
  const isReversible = Math.abs(pdfX - backToPdf.x) < 0.01 && Math.abs(pdfY - backToPdf.y) < 0.01
  
  console.log(`  Y-flip correct: ${yFlipCorrect ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`  Reversible: ${isReversible ? '✅ PASS' : '❌ FAIL'}`)
  console.log("")
})

// Test distance calculation with corrected coordinates
console.log("Distance Calculation Test (with corrected coordinates):")
console.log("-" .repeat(60))

// Simulate an annotation in the top half of the canvas
const annotationCentroid = { x: 637.5, y: 400 } // Canvas coordinates (middle-x, upper-y)

// Convert annotation centroid to PDF coordinates to see where it would be
const annotationInPdf = canvasToPdfCoordinates(annotationCentroid.x, annotationCentroid.y)
console.log(`Annotation centroid in canvas: (${annotationCentroid.x}, ${annotationCentroid.y})`)
console.log(`Annotation centroid in PDF: (${annotationInPdf.x.toFixed(2)}, ${annotationInPdf.y.toFixed(2)})`)
console.log("")

// Calculate distances from annotation to room codes
const roomDistances = testRoomCodes.map(({ code, pdfX, pdfY, description }) => {
  const canvasCoords = pdfToCanvasCoordinates(pdfX, pdfY)
  const dx = annotationCentroid.x - canvasCoords.x
  const dy = annotationCentroid.y - canvasCoords.y
  const distance = Math.sqrt(dx * dx + dy * dy)
  
  return {
    code,
    description,
    canvasCoords,
    distance: distance.toFixed(2)
  }
})

// Sort by distance
roomDistances.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance))

console.log("Room codes sorted by distance from annotation (top half):")
roomDistances.forEach(({ code, description, canvasCoords, distance }, index) => {
  console.log(`${index + 1}. ${code} (${description})`)
  console.log(`   Canvas coords: (${canvasCoords.x.toFixed(2)}, ${canvasCoords.y.toFixed(2)})`)
  console.log(`   Distance: ${distance}px`)
  console.log("")
})

console.log("=" .repeat(60))
console.log("Y-coordinate flip fix test completed!")
console.log("")
console.log("Expected behavior:")
console.log("- Room codes in 'Top half of PDF' should be closer to annotations in top of canvas")
console.log("- Room codes in 'Bottom half of PDF' should be closer to annotations in bottom of canvas")
console.log("- Y-coordinates should be properly flipped between PDF and Canvas coordinate systems")

// Test script for box scaling functionality
// This script tests the edge detection and scaling calculation functions

// Mock rectangle for testing
const testRectangle = {
  x: 100,
  y: 100,
  width: 200,
  height: 150
};

// Constants from the implementation
const HANDLE_SIZE = 10;
const EDGE_THRESHOLD = 8;

// Test function: getResizeHandle
function getResizeHandle(point, rect) {
  const { x, y, width, height } = rect;
  const handleSize = HANDLE_SIZE;
  const halfHandle = handleSize / 2;

  // Corner handles (priority over edge handles)
  if (Math.abs(point.x - x) <= halfHandle && Math.abs(point.y - y) <= halfHandle) {
    return 'nw'; // northwest
  }
  if (Math.abs(point.x - (x + width)) <= halfHandle && Math.abs(point.y - y) <= halfHandle) {
    return 'ne'; // northeast
  }
  if (Math.abs(point.x - x) <= halfHandle && Math.abs(point.y - (y + height)) <= halfHandle) {
    return 'sw'; // southwest
  }
  if (Math.abs(point.x - (x + width)) <= halfHandle && Math.abs(point.y - (y + height)) <= halfHandle) {
    return 'se'; // southeast
  }

  // Edge handles
  if (Math.abs(point.y - y) <= EDGE_THRESHOLD && point.x >= x && point.x <= x + width) {
    return 'n'; // north
  }
  if (Math.abs(point.y - (y + height)) <= EDGE_THRESHOLD && point.x >= x && point.x <= x + width) {
    return 's'; // south
  }
  if (Math.abs(point.x - x) <= EDGE_THRESHOLD && point.y >= y && point.y <= y + height) {
    return 'w'; // west
  }
  if (Math.abs(point.x - (x + width)) <= EDGE_THRESHOLD && point.y >= y && point.y <= y + height) {
    return 'e'; // east
  }

  return null;
}

// Test function: calculateResizedBounds
function calculateResizedBounds(originalBounds, handle, currentPoint, startPoint) {
  const { x, y, width, height } = originalBounds;
  let newX = x;
  let newY = y;
  let newWidth = width;
  let newHeight = height;

  const deltaX = currentPoint.x - startPoint.x;
  const deltaY = currentPoint.y - startPoint.y;

  switch (handle) {
    case 'nw': // northwest corner
      newX = x + deltaX;
      newY = y + deltaY;
      newWidth = width - deltaX;
      newHeight = height - deltaY;
      break;
    case 'ne': // northeast corner
      newY = y + deltaY;
      newWidth = width + deltaX;
      newHeight = height - deltaY;
      break;
    case 'sw': // southwest corner
      newX = x + deltaX;
      newWidth = width - deltaX;
      newHeight = height + deltaY;
      break;
    case 'se': // southeast corner
      newWidth = width + deltaX;
      newHeight = height + deltaY;
      break;
    case 'n': // north edge
      newY = y + deltaY;
      newHeight = height - deltaY;
      break;
    case 's': // south edge
      newHeight = height + deltaY;
      break;
    case 'w': // west edge
      newX = x + deltaX;
      newWidth = width - deltaX;
      break;
    case 'e': // east edge
      newWidth = width + deltaX;
      break;
  }

  // Ensure minimum size
  const minSize = 10;
  if (newWidth < minSize) {
    if (handle.includes('w')) {
      newX = x + width - minSize;
    }
    newWidth = minSize;
  }
  if (newHeight < minSize) {
    if (handle.includes('n')) {
      newY = y + height - minSize;
    }
    newHeight = minSize;
  }

  return { x: newX, y: newY, width: newWidth, height: newHeight };
}

// Test function: getPolygonVertexHandle
function getPolygonVertexHandle(point, polygon) {
  const handleSize = HANDLE_SIZE;
  const halfHandle = handleSize / 2;

  for (let i = 0; i < polygon.points.length; i++) {
    const vertex = polygon.points[i];
    if (Math.abs(point.x - vertex.x) <= halfHandle && Math.abs(point.y - vertex.y) <= halfHandle) {
      return { type: 'vertex', index: i };
    }
  }
  return null;
}

// Test cases
console.log('Testing Box Scaling Functions');
console.log('==============================');

// Test 1: Corner handle detection
console.log('\n1. Testing corner handle detection:');
const cornerTests = [
  { point: { x: 100, y: 100 }, expected: 'nw', description: 'Northwest corner' },
  { point: { x: 300, y: 100 }, expected: 'ne', description: 'Northeast corner' },
  { point: { x: 100, y: 250 }, expected: 'sw', description: 'Southwest corner' },
  { point: { x: 300, y: 250 }, expected: 'se', description: 'Southeast corner' }
];

cornerTests.forEach(test => {
  const result = getResizeHandle(test.point, testRectangle);
  const status = result === test.expected ? '✅ PASS' : '❌ FAIL';
  console.log(`  ${test.description}: ${status} (got: ${result}, expected: ${test.expected})`);
});

// Test 2: Enhanced edge handle detection (anywhere along edge)
console.log('\n2. Testing enhanced edge handle detection:');
const edgeTests = [
  { point: { x: 200, y: 100 }, expected: 'n', description: 'North edge center' },
  { point: { x: 150, y: 100 }, expected: 'n', description: 'North edge left' },
  { point: { x: 250, y: 100 }, expected: 'n', description: 'North edge right' },
  { point: { x: 200, y: 250 }, expected: 's', description: 'South edge center' },
  { point: { x: 100, y: 175 }, expected: 'w', description: 'West edge center' },
  { point: { x: 100, y: 130 }, expected: 'w', description: 'West edge upper' },
  { point: { x: 300, y: 175 }, expected: 'e', description: 'East edge center' },
  { point: { x: 300, y: 220 }, expected: 'e', description: 'East edge lower' }
];

edgeTests.forEach(test => {
  const result = getResizeHandle(test.point, testRectangle);
  const status = result === test.expected ? '✅ PASS' : '❌ FAIL';
  console.log(`  ${test.description}: ${status} (got: ${result}, expected: ${test.expected})`);
});

// Test 3: No handle detection
console.log('\n3. Testing no handle detection:');
const noHandleTests = [
  { point: { x: 200, y: 175 }, description: 'Center of rectangle' },
  { point: { x: 50, y: 50 }, description: 'Outside rectangle' },
  { point: { x: 200, y: 120 }, description: 'Inside rectangle, not near edge' }
];

noHandleTests.forEach(test => {
  const result = getResizeHandle(test.point, testRectangle);
  const status = result === null ? '✅ PASS' : '❌ FAIL';
  console.log(`  ${test.description}: ${status} (got: ${result}, expected: null)`);
});

// Test 4: Scaling calculations
console.log('\n4. Testing scaling calculations:');
const scalingTests = [
  {
    handle: 'se',
    startPoint: { x: 300, y: 250 },
    currentPoint: { x: 350, y: 300 },
    expectedWidth: 250,
    expectedHeight: 200,
    description: 'Southeast corner drag right and down'
  },
  {
    handle: 'nw',
    startPoint: { x: 100, y: 100 },
    currentPoint: { x: 120, y: 120 },
    expectedX: 120,
    expectedY: 120,
    expectedWidth: 180,
    expectedHeight: 130,
    description: 'Northwest corner drag right and down'
  },
  {
    handle: 'e',
    startPoint: { x: 300, y: 175 },
    currentPoint: { x: 320, y: 175 },
    expectedWidth: 220,
    expectedHeight: 150,
    description: 'East edge drag right'
  }
];

scalingTests.forEach(test => {
  const result = calculateResizedBounds(testRectangle, test.handle, test.currentPoint, test.startPoint);
  let passed = true;
  let details = [];

  if (test.expectedX !== undefined && Math.abs(result.x - test.expectedX) > 0.1) {
    passed = false;
    details.push(`x: got ${result.x}, expected ${test.expectedX}`);
  }
  if (test.expectedY !== undefined && Math.abs(result.y - test.expectedY) > 0.1) {
    passed = false;
    details.push(`y: got ${result.y}, expected ${test.expectedY}`);
  }
  if (test.expectedWidth !== undefined && Math.abs(result.width - test.expectedWidth) > 0.1) {
    passed = false;
    details.push(`width: got ${result.width}, expected ${test.expectedWidth}`);
  }
  if (test.expectedHeight !== undefined && Math.abs(result.height - test.expectedHeight) > 0.1) {
    passed = false;
    details.push(`height: got ${result.height}, expected ${test.expectedHeight}`);
  }

  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`  ${test.description}: ${status}`);
  if (!passed) {
    console.log(`    Issues: ${details.join(', ')}`);
  }
  console.log(`    Result: x=${result.x}, y=${result.y}, w=${result.width}, h=${result.height}`);
});

// Test 5: Polygon vertex detection
console.log('\n5. Testing polygon vertex detection:');
const testPolygon = {
  points: [
    { x: 150, y: 120 },
    { x: 250, y: 110 },
    { x: 280, y: 200 },
    { x: 200, y: 240 },
    { x: 120, y: 180 }
  ]
};

const polygonVertexTests = [
  { point: { x: 150, y: 120 }, expectedIndex: 0, description: 'First vertex' },
  { point: { x: 250, y: 110 }, expectedIndex: 1, description: 'Second vertex' },
  { point: { x: 280, y: 200 }, expectedIndex: 2, description: 'Third vertex' },
  { point: { x: 200, y: 240 }, expectedIndex: 3, description: 'Fourth vertex' },
  { point: { x: 120, y: 180 }, expectedIndex: 4, description: 'Fifth vertex' },
  { point: { x: 200, y: 150 }, expectedIndex: null, description: 'Not on vertex' }
];

polygonVertexTests.forEach(test => {
  const result = getPolygonVertexHandle(test.point, testPolygon);
  const resultIndex = result ? result.index : null;
  const status = resultIndex === test.expectedIndex ? '✅ PASS' : '❌ FAIL';
  console.log(`  ${test.description}: ${status} (got: ${resultIndex}, expected: ${test.expectedIndex})`);
});

console.log('\n==============================');
console.log('Enhanced scaling tests completed!');
console.log('✅ Rectangle edge detection enhanced');
console.log('✅ Polygon vertex detection added');

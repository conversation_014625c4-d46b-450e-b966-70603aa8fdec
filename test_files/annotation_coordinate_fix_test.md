# Annotation Coordinate System Fix Test Guide

## Problem Analysis

The issue is that rectangles are being created but are not visible on the canvas or in exported PDFs. This indicates a coordinate system problem where:

1. **Annotations are being stored** (counter updates, appear in sidebar)
2. **Drawing function is called** (based on code structure)
3. **Coordinates are incorrect** (rectangles drawn outside visible area or with invalid dimensions)

## Root Cause Investigation

### Potential Issues:
1. **Coordinate Transformation**: `screenToCanvasCoordinates` not accounting for canvas transforms properly
2. **Canvas Transform Conflicts**: Complex CSS transforms interfering with coordinate calculations
3. **Zoom/Pan Integration**: Canvas offset and zoom not properly integrated
4. **Drawing Context Issues**: Canvas context state problems

## Test Scenarios

### Test 1: Basic Drawing Verification
**Purpose**: Verify that the canvas drawing context works

**Expected Results**:
- [ ] Test red rectangle (50, 50, 100x100) should be visible on canvas
- [ ] This confirms the drawing context and canvas are functional

**If Test Rectangle Not Visible**:
- Canvas context is broken
- Canvas size issues
- CSS transform problems

### Test 2: Annotation Creation Verification
**Purpose**: Verify annotations are being created and stored

**Steps**:
1. Upload PDF
2. Select Rectangle tool
3. Click two points on canvas
4. Check browser console for coordinate logs
5. Check annotation counter in top-left

**Expected Results**:
- [ ] Console shows "Drawing rectangle: x, y, width, height"
- [ ] Annotation counter shows "1 annotation"
- [ ] Sidebar shows annotation in list

**If Annotations Not Created**:
- Mouse event handling broken
- Coordinate calculation returning invalid values
- State update not working

### Test 3: Coordinate Calculation Testing
**Purpose**: Verify coordinate transformation is working

**Manual Testing**:
1. Click at different positions on canvas
2. Check console logs for coordinates
3. Verify coordinates are within canvas bounds

**Expected Coordinate Ranges**:
- X coordinates: 0 to canvas.width
- Y coordinates: 0 to canvas.height
- Positive values only
- Reasonable values (not NaN, Infinity, or negative)

### Test 4: Canvas Transform Testing
**Purpose**: Test different zoom/pan states

**Steps**:
1. Create annotation at 100% zoom, no pan
2. Zoom to 200% and create annotation
3. Pan canvas and create annotation
4. Check if coordinates are consistent

**Expected Results**:
- [ ] Annotations visible at all zoom levels
- [ ] Coordinates adjust properly for zoom/pan
- [ ] No coordinate system conflicts

## Debugging Steps

### Step 1: Verify Canvas Drawing
```javascript
// In drawAnnotationsOnly function, add:
ctx.strokeStyle = '#ff0000'
ctx.lineWidth = 3
ctx.strokeRect(50, 50, 100, 100) // Test rectangle
```

### Step 2: Log Coordinate Calculations
```javascript
// In screenToCanvasCoordinates function, add:
console.log('Screen coords:', x, y)
console.log('Canvas coords:', canvasX, canvasY)
console.log('Canvas bounds:', canvas.width, canvas.height)
```

### Step 3: Verify Annotation Storage
```javascript
// In rectangle creation, add:
console.log('Creating annotation:', newAnnotation)
console.log('Current annotations:', getCurrentAnnotations())
```

### Step 4: Check Drawing Loop
```javascript
// In drawing loop, add:
console.log('Drawing annotation:', annotation.type, annotation)
```

## Common Coordinate Issues

### Issue 1: Negative Coordinates
**Cause**: Mouse coordinates outside canvas bounds
**Fix**: Add bounds checking in coordinate transformation

### Issue 2: Coordinates Outside Canvas
**Cause**: Zoom/pan not properly accounted for
**Fix**: Adjust coordinate transformation for current transform state

### Issue 3: Zero or Invalid Dimensions
**Cause**: Start and end points are the same or invalid
**Fix**: Add validation for minimum rectangle size

### Issue 4: Canvas Transform Conflicts
**Cause**: CSS transforms interfering with coordinate calculations
**Fix**: Simplify transform approach or adjust coordinate math

## Proposed Fixes

### Fix 1: Simplified Coordinate Transformation
```javascript
const screenToCanvasCoordinates = useCallback((x, y) => {
  const canvas = canvasRef.current
  if (!canvas) return { x: 0, y: 0 }

  const rect = canvas.getBoundingClientRect()
  
  // Simple transformation without complex transforms
  const canvasX = (x - rect.left) / zoom
  const canvasY = (y - rect.top) / zoom

  // Ensure coordinates are within bounds
  return {
    x: Math.max(0, Math.min(canvas.width, canvasX)),
    y: Math.max(0, Math.min(canvas.height, canvasY))
  }
}, [zoom])
```

### Fix 2: Simplified Canvas Transform
```css
/* Instead of complex transform, use simpler approach */
.pdf-canvas {
  transform: scale(zoom);
  transform-origin: center center;
  margin-left: canvasOffset.x;
  margin-top: canvasOffset.y;
}
```

### Fix 3: Add Coordinate Validation
```javascript
// Before creating rectangle, validate coordinates
if (width > 5 && height > 5 && 
    startX >= 0 && startY >= 0 && 
    startX + width <= canvas.width && 
    startY + height <= canvas.height) {
  // Create annotation
}
```

### Fix 4: Force Canvas Redraw
```javascript
// After creating annotation, force immediate redraw
updateCurrentAnnotations([...getCurrentAnnotations(), newAnnotation])
setTimeout(() => drawAnnotationsOnly(), 0) // Force redraw
```

## Success Criteria

The coordinate system is fixed when:
- ✅ Test rectangle is visible on canvas
- ✅ Created annotations appear immediately on canvas
- ✅ Annotation coordinates are within valid ranges
- ✅ Annotations work at all zoom levels
- ✅ Annotations work with canvas panning
- ✅ Exported PDF contains visible annotations
- ✅ No console errors during annotation creation

## Testing Checklist

### Basic Functionality
- [ ] Upload PDF successfully
- [ ] Test rectangle visible on canvas
- [ ] Rectangle tool creates visible annotations
- [ ] Polygon tool creates visible annotations
- [ ] Annotation counter updates correctly
- [ ] Annotations appear in sidebar list

### Coordinate Accuracy
- [ ] Annotations appear where clicked
- [ ] Rectangle dimensions match drag distance
- [ ] Coordinates are positive and within bounds
- [ ] No NaN or Infinity values in coordinates

### Transform Integration
- [ ] Annotations work at 50% zoom
- [ ] Annotations work at 200% zoom
- [ ] Annotations work with canvas panning
- [ ] Coordinate system remains consistent

### Export Functionality
- [ ] Annotations visible in exported PDF
- [ ] Annotation positions match canvas display
- [ ] Export coordinates properly transformed

## Emergency Fallback

If coordinate system remains broken:

1. **Reset to Simple System**:
   - Remove all canvas transforms
   - Use 1:1 coordinate mapping
   - Implement zoom/pan differently

2. **Alternative Approach**:
   - Store annotations in screen coordinates
   - Transform only during export
   - Avoid complex coordinate transformations

3. **Debug Mode**:
   - Add visual coordinate grid
   - Show mouse coordinates in real-time
   - Display annotation bounds as overlay

This systematic approach should identify and fix the coordinate system issues preventing annotation visibility.

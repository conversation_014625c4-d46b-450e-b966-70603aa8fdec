# Multi-Selection Deselection Test Plan

## Overview
This test plan verifies that clicking outside annotations while multiple annotations are selected will deselect all annotations, as requested by the user.

## Implementation Details
The fix was implemented in `src/App.jsx` in the `handleMouseDown` function by adding logic to clear selection when clicking outside any annotation in select mode.

### Code Changes
```javascript
// In handleMouseDown function, after checking for annotation clicks:
} else if (drawingMode === 'select') {
  // If in select mode and clicked outside any annotation, clear selection
  clearSelection()
}
```

## Test Scenarios

### Scenario 1: Basic Multi-Selection Deselection
**Objective**: Verify that clicking outside annotations deselects all selected annotations

**Steps**:
1. Open the application and load a PDF
2. Create 2-3 rectangle annotations
3. Switch to select mode (cursor icon in toolbar)
4. Click on first annotation to select it
5. Hold Ctrl and click on second annotation to add to selection
6. Hold Ctrl and click on third annotation to add to selection
7. Verify multiple annotations are highlighted/selected
8. Click on empty space (outside any annotation)
9. Verify all annotations are deselected (no longer highlighted)

**Expected Result**: All selected annotations should be deselected when clicking outside

### Scenario 2: Single Selection Deselection
**Objective**: Verify that single selection also gets deselected when clicking outside

**Steps**:
1. Select one annotation in select mode
2. Click outside the annotation
3. Verify annotation is deselected

**Expected Result**: Single selected annotation should be deselected

### Scenario 3: Mixed Annotation Types
**Objective**: Verify deselection works with different annotation types

**Steps**:
1. Create both rectangle and polygon annotations
2. Select multiple annotations of different types using Ctrl+click
3. Click outside all annotations
4. Verify all annotation types are deselected

**Expected Result**: All selected annotations (rectangles and polygons) should be deselected

### Scenario 4: Deselection During Resize
**Objective**: Verify behavior when clicking outside during resize operations

**Steps**:
1. Select an annotation
2. Hover over edge/corner to show resize cursor
3. Click outside the annotation
4. Verify annotation is deselected and resize mode is cancelled

**Expected Result**: Annotation should be deselected and resize operation should be cancelled

### Scenario 5: Mode-Specific Behavior
**Objective**: Verify deselection behavior is specific to select mode

**Steps**:
1. Select multiple annotations
2. Switch to rectangle mode
3. Click outside annotations
4. Verify selection is cleared (this was already working)
5. Switch to polygon mode
6. Click outside annotations
7. Verify selection is cleared and polygon point is added

**Expected Result**: Each mode should handle outside clicks appropriately

## Edge Cases to Test

### Edge Case 1: Rapid Clicking
**Steps**:
1. Select multiple annotations
2. Rapidly click outside annotations multiple times
3. Verify no errors occur and selection remains cleared

### Edge Case 2: Click on Canvas Border
**Steps**:
1. Select multiple annotations
2. Click on the canvas border/edge
3. Verify selection is cleared

### Edge Case 3: Click During Drag Operation
**Steps**:
1. Select multiple annotations
2. Start dragging one annotation
3. Click outside during drag
4. Verify appropriate behavior (drag should complete or cancel)

## Browser Compatibility
Test the functionality in:
- Chrome
- Firefox
- Safari
- Edge

## Performance Considerations
- Verify that deselection is immediate and responsive
- Check that there are no memory leaks with repeated selection/deselection
- Ensure smooth operation with large numbers of annotations

## Regression Testing
Verify that existing functionality still works:
- Ctrl+click multi-selection still works
- Single click selection still works
- Annotation creation in different modes still works
- Resize and drag operations still work
- Undo/redo functionality still works

## Success Criteria
✅ Clicking outside annotations in select mode clears all selections
✅ Single and multi-selection both work correctly
✅ No impact on existing functionality
✅ Smooth and responsive user experience
✅ Works across different browsers
✅ No console errors or warnings

## Notes
- The implementation is minimal and focused, adding only the necessary logic
- The change is backward compatible and doesn't affect other modes
- The fix addresses the specific user request without over-engineering

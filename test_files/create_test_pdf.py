#!/usr/bin/env python3
"""
Simple script to create a test PDF file for the annotation app
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import os

def create_test_pdf():
    filename = "test_document.pdf"
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # Page 1 - Building Floor Plan with Room Codes
    c.setFont("Helvetica-Bold", 24)
    c.drawString(100, height - 100, "Building Floor Plan - Level 1")

    c.set<PERSON><PERSON>("Helvetica", 12)
    c.drawString(100, height - 150, "Room Locations and Codes:")

    # Add room codes that match the CSV for testing
    c.drawString(150, height - 180, "BIOWASTE 01.E.28")
    c.drawString(150, height - 200, "CHEMICAL WASTE STORAGE 01.E.24")
    c.drawString(150, height - 220, "OFFICE 01.A.15")
    c.drawString(150, height - 240, "CONFERENCE ROOM 01.A.20")
    c.drawString(150, height - 260, "LABORATORY 01.B.05")
    c.drawString(150, height - 280, "STORAGE 01.B.12")

    # Add standalone room codes (more realistic scenario)
    c.setFont("Helvetica", 10)
    c.drawString(450, height - 180, "01.E.28")
    c.drawString(450, height - 220, "01.A.15")
    c.drawString(450, height - 260, "01.B.05")

    # Draw some reference shapes
    c.setStrokeColorRGB(0.5, 0.5, 0.5)
    c.rect(100, height - 350, 200, 50, fill=0)
    c.drawString(110, height - 330, "Room Layout Area")
    
    c.showPage()
    
    # Page 2 - Level 2 Floor Plan
    c.setFont("Helvetica-Bold", 20)
    c.drawString(100, height - 100, "Building Floor Plan - Level 2")

    c.setFont("Helvetica", 12)
    c.drawString(100, height - 150, "Level 2 Room Locations:")

    # Add Level 2 room codes
    c.drawString(150, height - 180, "MEETING ROOM 02.A.08")
    c.drawString(150, height - 200, "RESEARCH LAB 02.B.03")
    c.drawString(150, height - 220, "CLEAN ROOM 02.C.01")
    c.drawString(150, height - 240, "EQUIPMENT ROOM 02.C.07")

    # Add Level 3 room codes for additional testing
    c.drawString(150, height - 280, "EXECUTIVE OFFICE 03.A.01")
    c.drawString(150, height - 300, "TRAINING ROOM 03.B.10")

    # Standalone codes
    c.setFont("Helvetica", 10)
    c.drawString(450, height - 180, "02.A.08")
    c.drawString(450, height - 200, "02.B.03")
    c.drawString(450, height - 220, "02.C.01")
    c.drawString(450, height - 280, "03.A.01")

    # Add a light grid for reference
    c.setStrokeColorRGB(0.9, 0.9, 0.9)
    for i in range(0, int(width), 100):
        c.line(i, 0, i, height)
    for i in range(0, int(height), 100):
        c.line(0, i, width, i)
    
    c.save()
    print(f"Created {filename}")

if __name__ == "__main__":
    try:
        create_test_pdf()
    except ImportError:
        print("reportlab not installed. Installing...")
        os.system("pip install reportlab")
        create_test_pdf()

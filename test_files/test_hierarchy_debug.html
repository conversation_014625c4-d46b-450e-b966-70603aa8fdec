<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hierarchy Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Hierarchy Filter Debug Test</h1>
    
    <div class="test-section">
        <h2>Test: Room Collection Logic</h2>
        <p>Testing how rooms are collected when selecting different hierarchy levels:</p>
        <div id="test-result" class="result"></div>
    </div>

    <script>
        // Sample hierarchical data structure (as it would be built by CSV parser)
        const sampleHierarchyData = {
            "Building A": {
                "Floor 1": {
                    "Wing North": {
                        "_rooms": [
                            { name: "Room 101", path: ["Building A", "Floor 1", "Wing North", "Room 101"] },
                            { name: "Room 102", path: ["Building A", "Floor 1", "Wing North", "Room 102"] }
                        ]
                    },
                    "Wing South": {
                        "_rooms": [
                            { name: "Room 103", path: ["Building A", "Floor 1", "Wing South", "Room 103"] },
                            { name: "Room 104", path: ["Building A", "Floor 1", "Wing South", "Room 104"] }
                        ]
                    }
                },
                "Floor 2": {
                    "Wing North": {
                        "_rooms": [
                            { name: "Room 201", path: ["Building A", "Floor 2", "Wing North", "Room 201"] },
                            { name: "Room 202", path: ["Building A", "Floor 2", "Wing North", "Room 202"] }
                        ]
                    }
                }
            },
            "Building B": {
                "Floor 1": {
                    "Wing East": {
                        "_rooms": [
                            { name: "Room 301", path: ["Building B", "Floor 1", "Wing East", "Room 301"] },
                            { name: "Room 302", path: ["Building B", "Floor 1", "Wing East", "Room 302"] }
                        ]
                    }
                }
            }
        }

        // Simulate the getAllRoomsUnderPath function
        function getAllRoomsUnderPath(hierarchicalData, path) {
            if (!hierarchicalData || path.length === 0) {
                return []
            }

            let currentLevel = hierarchicalData
            
            // Navigate to the selected level
            for (const pathItem of path) {
                if (currentLevel[pathItem]) {
                    currentLevel = currentLevel[pathItem]
                } else {
                    return []
                }
            }

            // Recursively collect all rooms from this level and sub-levels
            const collectRooms = (level) => {
                let rooms = []
                
                // Add rooms at current level
                if (level._rooms) {
                    rooms.push(...level._rooms)
                }
                
                // Add rooms from sub-levels
                Object.keys(level).forEach(key => {
                    if (key !== '_rooms' && typeof level[key] === 'object') {
                        rooms.push(...collectRooms(level[key]))
                    }
                })
                
                return rooms
            }

            return collectRooms(currentLevel)
        }

        // Run tests
        function runTests() {
            let results = "🧪 Testing Room Collection Logic:\n\n"

            // Test 1: Select "Building A" - should get ALL rooms in Building A
            const buildingARooms = getAllRoomsUnderPath(sampleHierarchyData, ["Building A"])
            results += `1. Select "Building A":\n`
            results += `   Found ${buildingARooms.length} rooms:\n`
            buildingARooms.forEach(room => {
                results += `   - ${room.name} (${room.path.join(' → ')})\n`
            })
            results += `   ✓ Expected: Room 101, 102, 103, 104, 201, 202\n\n`

            // Test 2: Select "Building A → Floor 1" - should get rooms from Floor 1 only
            const floor1Rooms = getAllRoomsUnderPath(sampleHierarchyData, ["Building A", "Floor 1"])
            results += `2. Select "Building A → Floor 1":\n`
            results += `   Found ${floor1Rooms.length} rooms:\n`
            floor1Rooms.forEach(room => {
                results += `   - ${room.name} (${room.path.join(' → ')})\n`
            })
            results += `   ✓ Expected: Room 101, 102, 103, 104\n\n`

            // Test 3: Select "Building A → Floor 1 → Wing North" - should get specific wing rooms
            const wingNorthRooms = getAllRoomsUnderPath(sampleHierarchyData, ["Building A", "Floor 1", "Wing North"])
            results += `3. Select "Building A → Floor 1 → Wing North":\n`
            results += `   Found ${wingNorthRooms.length} rooms:\n`
            wingNorthRooms.forEach(room => {
                results += `   - ${room.name} (${room.path.join(' → ')})\n`
            })
            results += `   ✓ Expected: Room 101, 102\n\n`

            // Test 4: Select "Building B" - should get Building B rooms only
            const buildingBRooms = getAllRoomsUnderPath(sampleHierarchyData, ["Building B"])
            results += `4. Select "Building B":\n`
            results += `   Found ${buildingBRooms.length} rooms:\n`
            buildingBRooms.forEach(room => {
                results += `   - ${room.name} (${room.path.join(' → ')})\n`
            })
            results += `   ✓ Expected: Room 301, 302\n\n`

            // Validation
            const allRoomNames = [
                ...buildingARooms.map(r => r.name),
                ...buildingBRooms.map(r => r.name)
            ]
            
            results += `🎯 Summary:\n`
            results += `- Total unique rooms found: ${new Set(allRoomNames).size}\n`
            results += `- All room names are actual rooms (not hierarchy levels): ${allRoomNames.every(name => name.startsWith('Room'))}\n`
            results += `- No hierarchy levels in room names: ${!allRoomNames.some(name => name.includes('Building') || name.includes('Floor') || name.includes('Wing'))}\n`

            document.getElementById('test-result').textContent = results
            document.getElementById('test-result').className = 'result success'
        }

        // Run tests when page loads
        window.onload = runTests
    </script>
</body>
</html>

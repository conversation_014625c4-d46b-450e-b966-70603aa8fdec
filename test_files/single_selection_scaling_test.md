# Single Selection Scaling Test Guide

## Overview
This document provides test scenarios to verify that scaling functionality now works only for individual annotations (single selection) and is properly disabled for multi-selection.

## Changes Made
- ✅ Scaling only works when exactly one annotation is selected
- ✅ Resize handles are hidden when multiple annotations are selected
- ✅ Resize cursors don't appear when multiple annotations are selected
- ✅ Multi-selection scaling functionality completely removed

## Test Scenarios

### Test 1: Single Rectangle Scaling
**Expected Behavior**: Full scaling functionality available

1. Load a PDF and create a rectangle annotation
2. Switch to select mode and click on the rectangle to select it
3. **Verify**: Resize handles are visible (corner and edge handles)
4. Hover over edges and corners
5. **Verify**: Cursor changes to appropriate resize cursors
6. Drag from various handles
7. **Verify**: Rectangle scales properly

### Test 2: Single Polygon Scaling
**Expected Behavior**: Full vertex movement functionality available

1. Create a polygon annotation
2. Switch to select mode and click on the polygon to select it
3. **Verify**: Vertex handles are visible at each polygon point
4. Hover over vertices
5. **Verify**: <PERSON>ursor changes to 'move'
6. Drag vertices
7. **Verify**: Polygon reshapes properly

### Test 3: Multi-Selection Rectangle Behavior
**Expected Behavior**: Scaling completely disabled

1. Create multiple rectangle annotations
2. Select multiple rectangles using Ctrl+Click
3. **Verify**: NO resize handles are visible on any rectangle
4. Hover over edges and corners of selected rectangles
5. **Verify**: Cursor remains default (no resize cursors)
6. Try to drag from where handles would normally be
7. **Verify**: No scaling occurs, only normal dragging

### Test 4: Multi-Selection Polygon Behavior
**Expected Behavior**: Vertex movement completely disabled

1. Create multiple polygon annotations
2. Select multiple polygons using Ctrl+Click
3. **Verify**: NO vertex handles are visible on any polygon
4. Hover over vertices of selected polygons
5. **Verify**: Cursor remains default (no move cursor)
6. Try to drag vertices
7. **Verify**: No vertex movement occurs, only normal dragging

### Test 5: Mixed Multi-Selection Behavior
**Expected Behavior**: Scaling disabled for all types

1. Create both rectangle and polygon annotations
2. Select multiple annotations of different types (Ctrl+Click)
3. **Verify**: NO resize handles or vertex handles are visible
4. Hover over edges, corners, and vertices
5. **Verify**: Cursor remains default for all elements
6. **Verify**: No scaling or vertex movement is possible

### Test 6: Selection State Transitions
**Expected Behavior**: Handles appear/disappear based on selection count

1. Create multiple annotations
2. Select one annotation
3. **Verify**: Handles are visible
4. Add another annotation to selection (Ctrl+Click)
5. **Verify**: Handles immediately disappear
6. Remove one annotation from selection (Ctrl+Click)
7. **Verify**: Handles reappear for the remaining single selection

### Test 7: Undo/Redo with Single Selection
**Expected Behavior**: History works properly for single selection scaling

1. Select a single rectangle
2. Scale it using resize handles
3. Use Ctrl+Z to undo
4. **Verify**: Rectangle returns to original size
5. Use Ctrl+Y to redo
6. **Verify**: Scaling is reapplied
7. **Verify**: Operation name in history is "Resize Annotation"

## Visual Indicators

### Single Selection
- ✅ Green resize handles with white borders visible
- ✅ Appropriate resize cursors on hover
- ✅ Real-time scaling feedback

### Multi-Selection
- ✅ NO handles visible
- ✅ Default cursor only
- ✅ Normal selection highlighting only

## Expected Results Summary

| Scenario | Handles Visible | Resize Cursors | Scaling Works |
|----------|----------------|----------------|---------------|
| Single Rectangle | ✅ Yes | ✅ Yes | ✅ Yes |
| Single Polygon | ✅ Yes | ✅ Yes (move) | ✅ Yes |
| Multi Rectangle | ❌ No | ❌ No | ❌ No |
| Multi Polygon | ❌ No | ❌ No | ❌ No |
| Mixed Multi | ❌ No | ❌ No | ❌ No |

## Benefits of Single Selection Only

1. **Clarity**: Clear visual feedback about when scaling is available
2. **Precision**: Prevents accidental multi-object scaling
3. **Simplicity**: Easier to understand and use
4. **Performance**: Reduced complexity in calculations
5. **Predictability**: Consistent behavior across all annotation types

## Notes
- Regular dragging (moving) still works for multi-selection
- Selection highlighting still works normally for multi-selection
- Copy/paste operations work normally for multi-selection
- Only scaling/resizing is restricted to single selection

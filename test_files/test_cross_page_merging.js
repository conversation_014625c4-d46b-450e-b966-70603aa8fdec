// Test script to verify cross-page room code match merging
// This simulates how room codes found on different pages are merged together

// Mock the enhanced searchRoomCodesInPDFs function behavior
const simulateSearchRoomCodesInPDFs = () => {
  console.log("Simulating PDF search across multiple pages...")
  
  // Simulate finding room codes on different pages
  const page1Results = new Map([
    ['01.E.28', {
      code: '01.E.28',
      roomName: 'BIOWASTE 01.E.28',
      coordinates: { x: 100, y: 200, width: 50, height: 12 },
      pdfCoordinates: { x: 100, y: 200, width: 50, height: 12 },
      textItem: { text: '01.E.28', x: 100, y: 200, width: 50, height: 12 },
      allMatches: [
        {
          coordinates: { x: 100, y: 200, width: 50, height: 12 },
          pdfCoordinates: { x: 100, y: 200, width: 50, height: 12 },
          textItem: { text: '01.E.28', x: 100, y: 200, width: 50, height: 12 },
          pdfIndex: 0,
          pageIndex: 0
        },
        {
          coordinates: { x: 300, y: 400, width: 150, height: 12 },
          pdfCoordinates: { x: 300, y: 400, width: 150, height: 12 },
          textItem: { text: 'Storage area 01.E.28 location', x: 300, y: 400, width: 150, height: 12 },
          pdfIndex: 0,
          pageIndex: 0
        }
      ],
      matchCount: 2,
      found: true
    }],
    ['08.A.08', {
      code: '08.A.08',
      roomName: 'STORAGE 08.A.08',
      coordinates: { x: 500, y: 300, width: 50, height: 12 },
      pdfCoordinates: { x: 500, y: 300, width: 50, height: 12 },
      textItem: { text: '08.A.08', x: 500, y: 300, width: 50, height: 12 },
      allMatches: [
        {
          coordinates: { x: 500, y: 300, width: 50, height: 12 },
          pdfCoordinates: { x: 500, y: 300, width: 50, height: 12 },
          textItem: { text: '08.A.08', x: 500, y: 300, width: 50, height: 12 },
          pdfIndex: 0,
          pageIndex: 0
        }
      ],
      matchCount: 1,
      found: true
    }]
  ])

  // Simulate finding additional matches on page 2
  const page2Results = new Map([
    ['01.E.28', {
      code: '01.E.28',
      roomName: 'BIOWASTE 01.E.28',
      coordinates: { x: 700, y: 100, width: 120, height: 12 },
      pdfCoordinates: { x: 700, y: 100, width: 120, height: 12 },
      textItem: { text: 'Room 01.E.28 entrance', x: 700, y: 100, width: 120, height: 12 },
      allMatches: [
        {
          coordinates: { x: 700, y: 100, width: 120, height: 12 },
          pdfCoordinates: { x: 700, y: 100, width: 120, height: 12 },
          textItem: { text: 'Room 01.E.28 entrance', x: 700, y: 100, width: 120, height: 12 },
          pdfIndex: 0,
          pageIndex: 1
        },
        {
          coordinates: { x: 800, y: 600, width: 50, height: 12 },
          pdfCoordinates: { x: 800, y: 600, width: 50, height: 12 },
          textItem: { text: '01.E.28', x: 800, y: 600, width: 50, height: 12 },
          pdfIndex: 0,
          pageIndex: 1
        }
      ],
      matchCount: 2,
      found: true
    }]
  ])

  // Simulate the merging logic from PDFTextExtractor.jsx
  const allFoundCodes = new Map()

  // Process page 1 results
  console.log("\nProcessing Page 1 results:")
  page1Results.forEach((codeData, code) => {
    console.log(`  Found "${code}" with ${codeData.matchCount} matches on page 1`)
    allFoundCodes.set(code, {
      ...codeData,
      pdfIndex: 0,
      pdfName: 'test.pdf',
      allMatches: codeData.allMatches.map(match => ({
        ...match,
        pdfIndex: 0,
        pdfName: 'test.pdf'
      }))
    })
  })

  // Process page 2 results and merge
  console.log("\nProcessing Page 2 results:")
  page2Results.forEach((codeData, code) => {
    console.log(`  Found "${code}" with ${codeData.matchCount} matches on page 2`)
    
    if (!allFoundCodes.has(code)) {
      // First occurrence of this room code
      allFoundCodes.set(code, {
        ...codeData,
        pdfIndex: 0,
        pdfName: 'test.pdf',
        allMatches: codeData.allMatches.map(match => ({
          ...match,
          pdfIndex: 0,
          pdfName: 'test.pdf'
        }))
      })
    } else {
      // Room code already found on another page - merge the matches
      const existingData = allFoundCodes.get(code)
      const newMatches = codeData.allMatches.map(match => ({
        ...match,
        pdfIndex: 0,
        pdfName: 'test.pdf'
      }))
      
      // Combine all matches from different pages
      const combinedMatches = [...existingData.allMatches, ...newMatches]
      
      allFoundCodes.set(code, {
        ...existingData,
        // Update primary coordinates to the first match (for backward compatibility)
        coordinates: combinedMatches[0].coordinates,
        pdfCoordinates: combinedMatches[0].pdfCoordinates,
        textItem: combinedMatches[0].textItem,
        pdfIndex: combinedMatches[0].pdfIndex,
        pageIndex: combinedMatches[0].pageIndex,
        // Store all matches across all pages
        allMatches: combinedMatches,
        matchCount: combinedMatches.length
      })
      
      console.log(`    Merged "${code}" - now has ${combinedMatches.length} total matches across pages`)
    }
  })

  return allFoundCodes
}

// Test the cross-page merging
console.log("Testing Cross-Page Room Code Match Merging")
console.log("=" .repeat(60))

const mergedResults = simulateSearchRoomCodesInPDFs()

console.log("\n" + "=" .repeat(60))
console.log("FINAL MERGED RESULTS:")

mergedResults.forEach((codeData, code) => {
  console.log(`\n📍 Room Code: "${code}"`)
  console.log(`   Room Name: ${codeData.roomName}`)
  console.log(`   Total Matches: ${codeData.matchCount}`)
  console.log(`   Primary Coordinates: (${codeData.coordinates.x}, ${codeData.coordinates.y})`)
  
  if (codeData.allMatches && codeData.allMatches.length > 1) {
    console.log(`   All Match Locations:`)
    codeData.allMatches.forEach((match, index) => {
      console.log(`     ${index + 1}. Page ${match.pageIndex + 1}: (${match.coordinates.x}, ${match.coordinates.y}) - "${match.textItem.text}"`)
    })
  }
})

// Test distance calculation with merged results
console.log("\n" + "=" .repeat(60))
console.log("DISTANCE CALCULATION WITH MERGED RESULTS:")

const testUserPosition = { x: 750, y: 150 }
console.log(`\nUser interaction at: (${testUserPosition.x}, ${testUserPosition.y})`)

mergedResults.forEach((codeData, code) => {
  if (codeData.allMatches && codeData.allMatches.length > 1) {
    console.log(`\nRoom code "${code}" distance analysis:`)
    
    let closestMatch = null
    let minDistance = Infinity
    
    codeData.allMatches.forEach((match, index) => {
      const distance = Math.sqrt(
        Math.pow(match.coordinates.x - testUserPosition.x, 2) + 
        Math.pow(match.coordinates.y - testUserPosition.y, 2)
      )
      
      console.log(`  Match ${index + 1} (Page ${match.pageIndex + 1}): Distance ${Math.round(distance)}px`)
      
      if (distance < minDistance) {
        minDistance = distance
        closestMatch = match
      }
    })
    
    console.log(`  ✅ Closest match: Page ${closestMatch.pageIndex + 1} at (${closestMatch.coordinates.x}, ${closestMatch.coordinates.y}) - Distance: ${Math.round(minDistance)}px`)
  }
})

console.log("\n" + "=" .repeat(60))
console.log("CROSS-PAGE MERGING VERIFICATION:")
console.log("1. ✅ Multiple pages processed successfully")
console.log("2. ✅ Room codes found on different pages are merged")
console.log("3. ✅ All match locations preserved across pages")
console.log("4. ✅ Distance calculation works with merged results")
console.log("5. ✅ Closest match selection spans all pages")
console.log("6. ✅ Backward compatibility maintained with primary coordinates")

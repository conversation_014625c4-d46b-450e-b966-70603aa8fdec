# Box Scaling Functionality Test Guide

## Overview
This document provides a comprehensive test guide for the newly implemented box scaling functionality in the PDF annotation tool.

## Features Implemented

### 1. Edge Detection and Cursor Changes
- **Rectangle Corner Handles**: 10x10 pixel green squares at each corner (nw, ne, sw, se)
- **Rectangle Edge Detection**: Enhanced to work anywhere along the entire edge perimeter
- **Polygon Vertex Handles**: 10x10 pixel green squares at each polygon vertex
- **Dynamic Cursors**: Cursor changes based on handle position:
  - `nw-resize`, `ne-resize`, `sw-resize`, `se-resize` for rectangle corners
  - `n-resize`, `s-resize`, `w-resize`, `e-resize` for rectangle edges
  - `move` cursor for polygon vertices

### 2. Scaling Behavior
- **Rectangle Proportional Scaling**: Maintains aspect ratio when scaling from corners
- **Rectangle Directional Scaling**: Allows independent width/height scaling from edges
- **Rectangle Anchor Point**: Scales from the opposite edge/corner being dragged
- **Polygon Vertex Movement**: Individual vertices can be moved to reshape polygons
- **Minimum Size**: Enforces 10px minimum width and height for rectangles
- **Smooth Operation**: Real-time visual feedback during scaling

### 3. Single Selection Only
- **Individual Scaling**: Scaling only works when exactly one annotation is selected
- **No Multi-Selection**: Resize handles are hidden when multiple annotations are selected
- **Clear Visual Feedback**: Handles only appear for single selections

## Test Scenarios

### Basic Scaling Tests

#### Test 1: Single Rectangle Corner Scaling
1. Load a PDF and create a rectangle annotation
2. Switch to select mode and click on the rectangle to select it
3. Hover over each corner handle and verify cursor changes
4. Drag from each corner and verify:
   - Rectangle scales proportionally
   - Opposite corner remains fixed
   - Minimum size constraints are respected

#### Test 2: Single Rectangle Edge Scaling
1. Select a rectangle annotation
2. Hover over each edge handle and verify cursor changes
3. Drag from each edge and verify:
   - Rectangle scales in the appropriate direction only
   - Opposite edge remains fixed
   - Width or height changes independently

#### Test 3: Polygon Vertex Movement
1. Create a polygon annotation
2. Switch to select mode and click on the polygon to select it
3. Hover over each vertex handle and verify cursor changes to 'move'
4. Drag individual vertices and verify:
   - Vertex moves to new position
   - Polygon shape updates in real-time
   - Other vertices remain in place

#### Test 4: Multi-Selection Behavior
1. Create multiple rectangle annotations
2. Select multiple rectangles (Ctrl+click)
3. Verify that resize handles are NOT visible
4. Verify that cursor does NOT change when hovering over edges
5. Verify that scaling is disabled for multi-selection

### Edge Cases

#### Test 5: Enhanced Rectangle Edge Scaling
1. Create a rectangle annotation
2. Select the rectangle
3. Move mouse along any edge (not just midpoints)
4. Verify cursor changes to appropriate resize cursor anywhere along the edge
5. Drag from various points along edges
6. Verify scaling works from any point on the perimeter

#### Test 6: Minimum Size Constraints
1. Create a small rectangle
2. Try to scale it smaller than 10px in any dimension
3. Verify it stops at minimum size

#### Test 5: Large Rectangle Scaling
1. Create a large rectangle covering most of the canvas
2. Scale from various handles
3. Verify performance remains smooth

#### Test 6: Mixed Selection (Rectangle + Polygon)
1. Create both rectangle and polygon annotations
2. Select both types
3. Scale the rectangle
4. Verify polygon points scale relative to the rectangle

### Interaction Tests

#### Test 7: Mode Switching
1. Start scaling a rectangle
2. Verify other drawing modes are disabled during scaling
3. Complete the scaling operation
4. Verify normal mode switching works again

#### Test 8: Undo/Redo Integration
1. Scale a rectangle
2. Use Ctrl+Z to undo
3. Verify rectangle returns to original size
4. Use Ctrl+Y to redo
5. Verify scaling is reapplied

#### Test 9: Copy/Paste with Scaled Rectangles
1. Scale a rectangle
2. Copy it (Ctrl+C)
3. Paste it (Ctrl+V)
4. Verify the pasted rectangle maintains the scaled size

### Visual Feedback Tests

#### Test 10: Handle Visibility
1. Select rectangles of various sizes
2. Verify handles are always visible and properly positioned
3. Test with zoomed in/out views

#### Test 11: Cursor Accuracy
1. Move mouse slowly around rectangle edges
2. Verify cursor changes precisely at handle boundaries
3. Test cursor reset when moving away from handles

## Expected Behavior Summary

### Cursor States
- **Default**: When not over any handle
- **Resize Cursors**: When over specific handles
- **No Cursor Change**: During active scaling operation

### Scaling Constraints
- **Minimum Size**: 10px x 10px
- **Proportional**: Corner handles maintain aspect ratio
- **Directional**: Edge handles scale in one dimension

### Multi-Selection Rules
- **Primary Reference**: First selected annotation is the scaling reference
- **Relative Scaling**: Other annotations scale proportionally
- **Position Maintenance**: Relative positions preserved

### History Integration
- **Undo Support**: Scaling operations can be undone
- **Operation Names**: "Resize Annotation" or "Resize N Annotations"
- **State Preservation**: Pre-scaling state saved for undo

## Known Limitations
1. Polygon scaling is relative to rectangle bounds (simplified implementation)
2. Scaling only works in select mode
3. Touch/mobile scaling not yet implemented
4. No rotation during scaling

## Performance Notes
- Real-time scaling updates during drag
- Efficient handle detection using geometric calculations
- Minimal canvas redraws for smooth operation

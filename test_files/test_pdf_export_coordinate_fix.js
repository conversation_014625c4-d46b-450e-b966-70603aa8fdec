// Test script to verify the PDF export coordinate fix
// This tests that annotations appear in the correct position when exported to PDF

console.log("🔧 Testing PDF Export Coordinate Fix")
console.log("=" .repeat(60))

// Mock PDF and Canvas dimensions (based on typical values)
const mockPdfDimensions = {
  width: 612,   // Standard letter size PDF width in points
  height: 792   // Standard letter size PDF height in points
}

const mockCanvasDimensions = {
  width: 1275,  // Canvas width at 150 DPI (612 * 150/72)
  height: 1650  // Canvas height at 150 DPI (792 * 150/72)
}

// Simulate the canvasToPdfCoordinates function from PDFHandler.jsx
const canvasToPdfCoordinates = (canvasX, canvasY) => {
  const scaleX = mockPdfDimensions.width / mockCanvasDimensions.width
  const scaleY = mockPdfDimensions.height / mockCanvasDimensions.height
  
  // Convert canvas coordinates (top-left origin) to PDF coordinates (bottom-left origin)
  // Canvas Y needs to be flipped relative to PDF coordinate system
  const pdfX = canvasX * scaleX
  const pdfY = mockPdfDimensions.height - (canvasY * scaleY)
  
  return {
    x: pdfX,
    y: pdfY
  }
}

// Test annotations at different positions on the canvas
const testAnnotations = [
  {
    id: 'rect1',
    type: 'rectangle',
    x: 100,      // Canvas coordinates
    y: 100,      // Top area of canvas
    width: 200,
    height: 150,
    description: 'Top-left area rectangle'
  },
  {
    id: 'rect2', 
    type: 'rectangle',
    x: 500,      // Canvas coordinates
    y: 1400,     // Bottom area of canvas
    width: 300,
    height: 200,
    description: 'Bottom-right area rectangle'
  },
  {
    id: 'rect3',
    type: 'rectangle',
    x: 300,      // Canvas coordinates
    y: 800,      // Middle area of canvas
    width: 250,
    height: 100,
    description: 'Center area rectangle'
  }
]

console.log("Testing Rectangle Coordinate Conversion:")
console.log("-" .repeat(60))

testAnnotations.forEach(annotation => {
  console.log(`\n📍 ${annotation.description}`)
  console.log(`Canvas coordinates: (${annotation.x}, ${annotation.y}) ${annotation.width}x${annotation.height}`)
  
  // Convert canvas coordinates to PDF coordinates (as done in export)
  const pdfTopLeft = canvasToPdfCoordinates(annotation.x, annotation.y)
  const pdfBottomRight = canvasToPdfCoordinates(annotation.x + annotation.width, annotation.y + annotation.height)
  
  console.log(`PDF Top-Left: (${pdfTopLeft.x.toFixed(2)}, ${pdfTopLeft.y.toFixed(2)})`)
  console.log(`PDF Bottom-Right: (${pdfBottomRight.x.toFixed(2)}, ${pdfBottomRight.y.toFixed(2)})`)
  
  // FIXED: Use the corrected coordinate calculation (no double flip)
  const x = pdfTopLeft.x
  const y = pdfBottomRight.y  // Use bottom-right Y directly (already flipped by canvasToPdfCoordinates)
  const rectWidth = pdfBottomRight.x - pdfTopLeft.x
  const rectHeight = pdfTopLeft.y - pdfBottomRight.y  // Height calculation: top Y - bottom Y
  
  console.log(`PDF Export coordinates: (${x.toFixed(2)}, ${y.toFixed(2)}) ${rectWidth.toFixed(2)}x${rectHeight.toFixed(2)}`)
  
  // Verify that the rectangle has positive dimensions
  const hasValidDimensions = rectWidth > 0 && rectHeight > 0
  console.log(`Valid dimensions: ${hasValidDimensions ? '✅ YES' : '❌ NO'}`)
  
  // Verify that the rectangle is within PDF bounds
  const withinBounds = x >= 0 && y >= 0 && (x + rectWidth) <= mockPdfDimensions.width && (y + rectHeight) <= mockPdfDimensions.height
  console.log(`Within PDF bounds: ${withinBounds ? '✅ YES' : '❌ NO'}`)
})

console.log("\n🧪 Testing Polygon Coordinate Conversion:")
console.log("-" .repeat(60))

// Test polygon with multiple points
const testPolygon = {
  id: 'poly1',
  type: 'polygon',
  points: [
    { x: 200, y: 300 },  // Canvas coordinates
    { x: 400, y: 250 },
    { x: 450, y: 500 },
    { x: 150, y: 550 }
  ],
  description: 'Test polygon'
}

console.log(`\n📍 ${testPolygon.description}`)
console.log(`Canvas points:`)
testPolygon.points.forEach((point, index) => {
  console.log(`  Point ${index + 1}: (${point.x}, ${point.y})`)
})

console.log(`PDF points (FIXED - no double flip):`)
testPolygon.points.forEach((point, index) => {
  const pdfPoint = canvasToPdfCoordinates(point.x, point.y)
  console.log(`  Point ${index + 1}: (${pdfPoint.x.toFixed(2)}, ${pdfPoint.y.toFixed(2)})`)
  
  // Verify that the point is within PDF bounds
  const withinBounds = pdfPoint.x >= 0 && pdfPoint.y >= 0 && pdfPoint.x <= mockPdfDimensions.width && pdfPoint.y <= mockPdfDimensions.height
  console.log(`    Within bounds: ${withinBounds ? '✅ YES' : '❌ NO'}`)
})

console.log("\n🎯 Summary of Fix:")
console.log("-" .repeat(60))
console.log("❌ BEFORE: Double Y-coordinate flip caused annotations to appear in wrong positions")
console.log("   - canvasToPdfCoordinates() already flipped Y-coordinate")
console.log("   - exportAnnotatedPDF() flipped Y-coordinate again: y = height - pdfBottomRight.y")
console.log("   - Result: Annotations appeared vertically mirrored")
console.log("")
console.log("✅ AFTER: Single Y-coordinate flip in canvasToPdfCoordinates() only")
console.log("   - canvasToPdfCoordinates() handles Y-coordinate flip")
console.log("   - exportAnnotatedPDF() uses coordinates directly: y = pdfBottomRight.y")
console.log("   - Result: Annotations appear in correct positions")
console.log("")
console.log("🔧 Files Modified:")
console.log("   - src/components/ExportUtils.jsx (lines 177-216)")
console.log("   - Fixed rectangle export coordinate calculation")
console.log("   - Fixed polygon export coordinate calculation")
console.log("")
console.log("✅ The coordinate mismatch issue should now be resolved!")

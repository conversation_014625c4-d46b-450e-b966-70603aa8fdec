# Testing Distance-Sorted Room Dropdown

## 🧪 Complete Testing Guide

Follow these steps to test the distance-based room sorting functionality:

### **Prerequisites**
1. Application running at `http://localhost:5174/`
2. `sample_rooms.csv` file (contains room codes)
3. `test_document.pdf` file (contains matching room codes)

### **Step 1: Setup and File Upload**

1. **Upload CSV File**:
   - Click "Upload CSV" button
   - Select `sample_rooms.csv`
   - ✅ Verify: <PERSON>sol<PERSON> shows extracted room codes

2. **Upload PDF File**:
   - Click "Upload PDF" button
   - Select `test_document.pdf`
   - ✅ Verify: PDF loads and room code search starts automatically

3. **Wait for Search Completion**:
   - ✅ Verify: Search status shows completion
   - ✅ Verify: Found room codes are displayed
   - ✅ Verify: Distance sorting toggle (📏) appears in toolbar

### **Step 2: Test Distance Sorting Toggle**

1. **Check Toggle State**:
   - ✅ Verify: 📏 button is active (highlighted) by default
   - ✅ Verify: Toolt<PERSON> shows "Disable distance-based room sorting"

2. **Toggle Off**:
   - Click 📏 button to disable
   - ✅ Verify: But<PERSON> becomes inactive (not highlighted)
   - ✅ Verify: Toolt<PERSON> shows "Enable distance-based room sorting"

3. **Toggle On**:
   - Click 📏 button to enable
   - ✅ Verify: Button becomes active again

### **Step 3: Test Distance-Sorted Dropdown**

1. **Create Rectangle Annotation**:
   - Select Rectangle tool (⬜)
   - Draw rectangle near coordinates (520, 320) - close to room code "01.E.24"
   - ✅ Verify: Distance-sorted dropdown appears

2. **Verify Dropdown Features**:
   - ✅ Header shows "Assign Room Name (X nearby)"
   - ✅ Distance toggle button (📏) visible in header
   - ✅ Search placeholder mentions "sorted by distance"

3. **Check Room Sorting**:
   - ✅ Verify: Rooms with 🎯 or 🔴 indicators appear first
   - ✅ Verify: Distance information shows (e.g., "30px", "90px")
   - ✅ Verify: "Nearby Rooms" section header appears
   - ✅ Verify: Closest rooms have green background highlighting

### **Step 4: Test Different Annotation Locations**

1. **Close to Room Code**:
   - Draw rectangle at (520, 320)
   - ✅ Expected: "CHEMICAL WASTE STORAGE 01.E.24" appears first with 🎯
   - ✅ Expected: Distance shows < 50px

2. **Medium Distance**:
   - Draw rectangle at (600, 400)
   - ✅ Expected: "OFFICE 01.A.15" appears first with 🔴 or 🟡
   - ✅ Expected: Distance shows 50-200px

3. **Far from Any Room Code**:
   - Draw rectangle at (100, 100)
   - ✅ Expected: All rooms with coordinates show larger distances
   - ✅ Expected: Rooms without coordinates appear at bottom with 📍

### **Step 5: Test Polygon Annotations**

1. **Create Polygon**:
   - Select Polygon tool (🔺)
   - Click multiple points around (545, 315)
   - Press Enter to complete
   - ✅ Verify: Distance-sorted dropdown appears
   - ✅ Verify: "MEETING ROOM 02.A.08" appears first (closest to that location)

### **Step 6: Test Dropdown Interactions**

1. **Distance Information Toggle**:
   - Press Tab key while dropdown is open
   - ✅ Verify: Distance information toggles on/off
   - ✅ Verify: Room names remain sorted by distance

2. **Search Functionality**:
   - Type in search box
   - ✅ Verify: Filtered results maintain distance sorting
   - ✅ Verify: Distance indicators remain visible

3. **Keyboard Navigation**:
   - Use Arrow keys to navigate
   - ✅ Verify: Selection follows distance-sorted order
   - Press Enter to select
   - ✅ Verify: Correct room name is assigned

### **Step 7: Test Fallback Behavior**

1. **Disable Distance Sorting**:
   - Click 📏 button to disable
   - Create new annotation
   - ✅ Verify: Regular dropdown appears (no distance info)
   - ✅ Verify: Rooms sorted alphabetically

2. **No Room Codes Scenario**:
   - Clear CSV data or use CSV without room codes
   - Create annotation
   - ✅ Verify: Regular dropdown appears
   - ✅ Verify: No distance sorting toggle in toolbar

### **Step 8: Performance Testing**

1. **Large CSV File**:
   - Upload CSV with many room names
   - Create annotation
   - ✅ Verify: Dropdown appears quickly (< 1 second)
   - ✅ Verify: Sorting is accurate

2. **Multiple Annotations**:
   - Create several annotations in different locations
   - ✅ Verify: Each dropdown shows appropriate distance sorting
   - ✅ Verify: No performance degradation

### **Step 9: Visual Verification**

1. **Distance Indicators**:
   - ✅ 🎯 for very close (< 50px)
   - ✅ 🔴 for close (50-100px)
   - ✅ 🟡 for medium (100-200px)
   - ✅ 🟠 for far (200-500px)
   - ✅ ⚪ for very far (> 500px)
   - ✅ 📍 for no coordinates

2. **Background Highlighting**:
   - ✅ Dark green for very close rooms
   - ✅ Light green for close rooms
   - ✅ Default background for distant rooms

3. **Distance Text**:
   - ✅ Formatted as "30px", "90px", etc.
   - ✅ "No location" for rooms without coordinates
   - ✅ Monospace font in small gray boxes

### **Step 10: Browser Console Testing**

1. **Run Test Script**:
   ```javascript
   // Copy and paste test_distance_sorting.js content into console
   // Verify all tests pass
   ```

2. **Check Cache**:
   ```javascript
   console.log('Room code cache:', window.roomCodeCache)
   // Verify coordinates are properly cached
   ```

## 🎯 Expected Results Summary

### **Distance Sorting Order** (for annotation at 550, 325):
1. 🎯 MEETING ROOM 02.A.08 (~11px)
2. 🎯 CHEMICAL WASTE STORAGE 01.E.24 (~30px)
3. 🔴 OFFICE 01.A.15 (~90px)
4. 🟠 LABORATORY 01.B.05 (~370px)
5. 📍 Rooms without coordinates (alphabetical)

### **UI Behavior**:
- ✅ Dropdown appears immediately after annotation completion
- ✅ Closest rooms highlighted with green backgrounds
- ✅ Distance information clearly visible
- ✅ Toggle button works correctly
- ✅ Fallback to regular dropdown when needed

### **Performance**:
- ✅ No noticeable delay in dropdown appearance
- ✅ Smooth scrolling and interaction
- ✅ Responsive to user input

## 🐛 Troubleshooting

### **Distance Sorting Not Working**:
- Check if room code search completed successfully
- Verify 📏 toggle is enabled
- Ensure room codes exist in both CSV and PDF

### **Incorrect Distances**:
- Verify annotation coordinates
- Check room code coordinates in cache
- Test with known coordinates

### **Visual Issues**:
- Check CSS loading
- Verify emoji support in browser
- Test responsive design

## ✅ Success Criteria

The distance-sorted dropdown is working correctly if:

1. ✅ Rooms are sorted by distance from annotation centroid
2. ✅ Visual indicators show distance categories
3. ✅ Close rooms are highlighted appropriately
4. ✅ Toggle button enables/disables feature
5. ✅ Fallback works when coordinates unavailable
6. ✅ Performance is smooth and responsive
7. ✅ All original dropdown features still work
8. ✅ Distance information is accurate and helpful

This feature significantly improves the user experience by making room assignment more intuitive and efficient!

# Undo/Redo Functionality Test Plan

## Overview
This document outlines the test plan for the newly implemented undo/redo functionality for bounding box operations.

## Test Environment Setup
1. Open the application at http://localhost:5175/
2. Upload a PDF file to enable annotation features
3. Verify that undo/redo buttons are visible in the toolbar
4. Verify that undo/redo buttons are initially disabled (no history)

## Test Cases

### 1. Rectangle Creation and Undo/Redo
**Test Steps:**
1. Select Rectangle tool
2. Draw a rectangle on the PDF
3. Verify undo button is enabled, redo button is disabled
4. Click undo button or press Ctrl+Z
5. Verify rectangle disappears
6. Verify undo button is disabled, redo button is enabled
7. Click redo button or press Ctrl+Y
8. Verify rectangle reappears
9. Verify undo button is enabled, redo button is disabled

**Expected Results:**
- Rectangle creation is properly undone and redone
- Button states change correctly
- Tooltips show operation descriptions

### 2. Polygon Creation and Undo/Redo
**Test Steps:**
1. Select Polygon tool
2. Create a polygon with multiple points
3. Finish the polygon
4. Test undo/redo as in Test Case 1

**Expected Results:**
- Polygon creation is properly undone and redone
- All polygon points are restored correctly

### 3. Annotation Deletion and Undo/Redo
**Test Steps:**
1. Create a rectangle annotation
2. Select the annotation
3. Delete it using Delete key or delete button
4. Verify undo button is enabled
5. Click undo to restore the annotation
6. Verify annotation reappears and is selected
7. Click redo to delete it again

**Expected Results:**
- Deletion is properly undone and redone
- Selection state is maintained correctly

### 4. Annotation Movement and Undo/Redo
**Test Steps:**
1. Create a rectangle annotation
2. Select it and drag it to a new position
3. Release the mouse to complete the move
4. Verify undo button is enabled
5. Click undo to restore original position
6. Verify annotation moves back to original position
7. Click redo to move it back to new position

**Expected Results:**
- Movement is properly undone and redone
- Original and new positions are accurate

### 5. Label Update and Undo/Redo
**Test Steps:**
1. Create an annotation
2. Edit its label/name
3. Verify undo button is enabled
4. Click undo to restore original label
5. Click redo to restore new label

**Expected Results:**
- Label changes are properly undone and redone

### 6. Multiple Operations and History Limits
**Test Steps:**
1. Perform 7-8 different operations (create, move, delete, etc.)
2. Verify only last 5 operations can be undone
3. Undo all available operations
4. Verify only last 5 redo operations are available

**Expected Results:**
- History is limited to 5 undo and 5 redo operations
- Oldest operations are removed when limit is exceeded

### 7. Page/PDF Switch Edge Cases
**Test Steps:**
1. Create annotations on page 1
2. Switch to page 2
3. Verify undo/redo buttons reflect page 2 history (should be disabled)
4. Create annotations on page 2
5. Switch back to page 1
6. Verify page 1 history is preserved

**Expected Results:**
- Each page maintains separate history
- Switching pages doesn't affect other pages' history

### 8. Keyboard Shortcuts
**Test Steps:**
1. Create an annotation
2. Press Ctrl+Z to undo
3. Press Ctrl+Y to redo
4. Verify shortcuts work when not in input fields

**Expected Results:**
- Keyboard shortcuts work correctly
- Shortcuts are ignored when typing in input fields

### 9. Room Assignment with Undo/Redo
**Test Steps:**
1. Upload a CSV file with room names
2. Create an annotation
3. Assign a room name via dropdown
4. Verify undo button is enabled
5. Click undo to remove the annotation
6. Click redo to restore it with room assignment

**Expected Results:**
- Room assignments are properly tracked in history
- Undo/redo works with room dropdown workflow

### 10. Multiple Selection Operations
**Test Steps:**
1. Create multiple annotations
2. Select multiple annotations
3. Delete them all at once
4. Verify undo restores all annotations
5. Test moving multiple annotations and undo/redo

**Expected Results:**
- Multi-selection operations are properly tracked
- All selected annotations are affected by undo/redo

## Manual Testing Checklist

### UI/UX Verification
- [ ] Undo button shows correct icon (↶)
- [ ] Redo button shows correct icon (↷)
- [ ] Buttons are disabled when no operations available
- [ ] Tooltips show operation descriptions
- [ ] Buttons have proper visual feedback (opacity changes)

### Functionality Verification
- [ ] Rectangle creation undo/redo works
- [ ] Polygon creation undo/redo works
- [ ] Annotation deletion undo/redo works
- [ ] Annotation movement undo/redo works
- [ ] Label update undo/redo works
- [ ] Keyboard shortcuts (Ctrl+Z, Ctrl+Y) work
- [ ] History limits (5 operations) are enforced
- [ ] Page switching preserves separate histories
- [ ] Room assignment undo/redo works
- [ ] Multiple selection operations work

### Edge Cases Verification
- [ ] Undo/redo with no annotations
- [ ] Undo/redo after page switch
- [ ] Undo/redo after PDF switch
- [ ] Undo/redo with overlapping annotations
- [ ] Undo/redo during active drawing operations
- [ ] Keyboard shortcuts ignored in input fields

## Known Limitations
1. History is limited to 5 operations per direction (undo/redo)
2. History is maintained per PDF page, not globally
3. Some complex operations may not be perfectly reversible
4. Performance may be affected with very large annotation sets

## Success Criteria
- All test cases pass without errors
- UI is responsive and intuitive
- No data loss or corruption during undo/redo operations
- Performance remains acceptable
- Keyboard shortcuts work reliably

// Test script to verify coordinate conversion between PDF and Canvas
// This simulates the coordinate conversion logic

// Mock PDF and Canvas dimensions (based on typical values)
const mockPdfDimensions = {
  width: 612,   // Standard letter size PDF width in points
  height: 792   // Standard letter size PDF height in points
}

const mockCanvasDimensions = {
  width: 1275,  // Canvas width at 150 DPI (612 * 150/72)
  height: 1650  // Canvas height at 150 DPI (792 * 150/72)
}

// Simulate the coordinate conversion functions
const canvasToPdfCoordinates = (canvasX, canvasY) => {
  const scaleX = mockPdfDimensions.width / mockCanvasDimensions.width
  const scaleY = mockPdfDimensions.height / mockCanvasDimensions.height
  
  return {
    x: canvasX * scaleX,
    y: canvasY * scaleY
  }
}

const pdfToCanvasCoordinates = (pdfX, pdfY) => {
  const scaleX = mockCanvasDimensions.width / mockPdfDimensions.width
  const scaleY = mockCanvasDimensions.height / mockPdfDimensions.height
  
  return {
    x: pdfX * scaleX,
    y: pdfY * scaleY
  }
}

// Test cases
console.log("Testing Coordinate Conversion:")
console.log("=" .repeat(60))
console.log(`PDF Dimensions: ${mockPdfDimensions.width} x ${mockPdfDimensions.height}`)
console.log(`Canvas Dimensions: ${mockCanvasDimensions.width} x ${mockCanvasDimensions.height}`)
console.log("=" .repeat(60))

// Test room code coordinates (simulating text found in PDF)
const testRoomCodes = [
  { code: "01.E.24", pdfX: 450, pdfY: 200 },
  { code: "01.A.15", pdfX: 400, pdfY: 220 },
  { code: "01.B.05", pdfX: 400, pdfY: 260 },
  { code: "02.A.08", pdfX: 450, pdfY: 180 },
  { code: "02.B.03", pdfX: 450, pdfY: 200 }
]

console.log("Room Code Coordinate Conversions:")
console.log("-" .repeat(60))

testRoomCodes.forEach(({ code, pdfX, pdfY }) => {
  const canvasCoords = pdfToCanvasCoordinates(pdfX, pdfY)
  const backToPdf = canvasToPdfCoordinates(canvasCoords.x, canvasCoords.y)
  
  console.log(`${code}:`)
  console.log(`  PDF:    (${pdfX}, ${pdfY})`)
  console.log(`  Canvas: (${canvasCoords.x.toFixed(2)}, ${canvasCoords.y.toFixed(2)})`)
  console.log(`  Back:   (${backToPdf.x.toFixed(2)}, ${backToPdf.y.toFixed(2)})`)
  
  // Check if conversion is reversible (should be very close to original)
  const xDiff = Math.abs(pdfX - backToPdf.x)
  const yDiff = Math.abs(pdfY - backToPdf.y)
  const isAccurate = xDiff < 0.01 && yDiff < 0.01
  
  console.log(`  Accuracy: ${isAccurate ? '✅ PASS' : '❌ FAIL'} (diff: ${xDiff.toFixed(4)}, ${yDiff.toFixed(4)})`)
  console.log("")
})

// Test edge cases
console.log("Edge Case Tests:")
console.log("-" .repeat(60))

const edgeCases = [
  { name: "Top-left corner", pdfX: 0, pdfY: 0 },
  { name: "Bottom-right corner", pdfX: mockPdfDimensions.width, pdfY: mockPdfDimensions.height },
  { name: "Center", pdfX: mockPdfDimensions.width / 2, pdfY: mockPdfDimensions.height / 2 }
]

edgeCases.forEach(({ name, pdfX, pdfY }) => {
  const canvasCoords = pdfToCanvasCoordinates(pdfX, pdfY)
  const backToPdf = canvasToPdfCoordinates(canvasCoords.x, canvasCoords.y)
  
  console.log(`${name}:`)
  console.log(`  PDF -> Canvas -> PDF: (${pdfX}, ${pdfY}) -> (${canvasCoords.x.toFixed(2)}, ${canvasCoords.y.toFixed(2)}) -> (${backToPdf.x.toFixed(2)}, ${backToPdf.y.toFixed(2)})`)
  
  const isAccurate = Math.abs(pdfX - backToPdf.x) < 0.01 && Math.abs(pdfY - backToPdf.y) < 0.01
  console.log(`  Result: ${isAccurate ? '✅ PASS' : '❌ FAIL'}`)
  console.log("")
})

// Calculate scale factors
const scaleX = mockCanvasDimensions.width / mockPdfDimensions.width
const scaleY = mockCanvasDimensions.height / mockCanvasDimensions.height

console.log("Scale Factors:")
console.log("-" .repeat(60))
console.log(`X Scale (Canvas/PDF): ${scaleX.toFixed(6)}`)
console.log(`Y Scale (Canvas/PDF): ${scaleY.toFixed(6)}`)
console.log(`Expected scale (150 DPI / 72 DPI): ${(150/72).toFixed(6)}`)
console.log(`Scale match: ${Math.abs(scaleX - 150/72) < 0.001 ? '✅ PASS' : '❌ FAIL'}`)

console.log("\n" + "=" .repeat(60))
console.log("Coordinate conversion test completed!")
console.log("This verifies that PDF coordinates can be correctly converted to canvas coordinates")
console.log("and that the conversion is reversible with high accuracy.")

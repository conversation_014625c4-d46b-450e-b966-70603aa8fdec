# Coordinate System Verification Test

## Overview
This test verifies that annotations are positioned correctly in both the web viewer and exported files, regardless of zoom level or display size.

## Coordinate System Architecture

### 1. **Canvas Coordinates** (Internal Storage)
- Used for storing annotation positions internally
- Based on the rendered canvas size (300 DPI)
- Origin: Top-left corner (0,0)
- Units: Pixels at 300 DPI resolution

### 2. **PDF Coordinates** (Export Format)
- Used for JSON and PDF exports
- Based on original PDF dimensions (scale 1)
- Origin: Top-left for JSON, Bottom-left for PDF rendering
- Units: PDF points (1/72 inch)

### 3. **Screen Coordinates** (User Interaction)
- Mouse/touch coordinates from browser events
- Affected by zoom level and canvas scaling
- Converted to canvas coordinates for processing

## Coordinate Conversion Functions

### `screenToCanvasCoordinates(x, y)`
- Converts mouse coordinates to canvas coordinates
- Accounts for zoom level and canvas position
- Used for: Mouse events, drawing, selection

### `canvasToPdfCoordinates(canvasX, canvasY)`
- Converts canvas coordinates to PDF coordinates
- Scales from 300 DPI canvas to original PDF dimensions
- Used for: JSON export, PDF export

### `pdfToCanvasCoordinates(pdfX, pdfY)` (if needed)
- Converts PDF coordinates back to canvas coordinates
- Used for: Loading saved annotations

## Test Scenarios

### Test 1: Basic Positioning Accuracy
1. **Setup**: Upload test PDF, set zoom to 100%
2. **Action**: Create rectangle at top-left corner (should be near 0,0)
3. **Verify**: 
   - Canvas coordinates should be small positive numbers
   - PDF coordinates in JSON should be small positive numbers
   - PDF export should show rectangle at top-left

### Test 2: Zoom Independence
1. **Setup**: Upload test PDF
2. **Action**: 
   - Create annotation at 50% zoom
   - Create annotation at 200% zoom at same visual position
3. **Verify**: 
   - Both annotations should have identical coordinates in exports
   - Visual positions should match in web viewer

### Test 3: Multi-Page Consistency
1. **Setup**: Upload multi-page PDF
2. **Action**: Create identical annotations on different pages
3. **Verify**: 
   - Coordinates should be identical across pages
   - Page indices should be correct in exports

### Test 4: Complex Shapes
1. **Setup**: Upload test PDF
2. **Action**: Create polygon with multiple points
3. **Verify**: 
   - All points maintain relative positions
   - Shape appears identical in PDF export

### Test 5: Large Document Handling
1. **Setup**: Upload large PDF (A0 or larger)
2. **Action**: Create annotations at various positions
3. **Verify**: 
   - Coordinates scale properly
   - No precision loss in large coordinate values

## Expected Results

### JSON Export Verification
```json
{
  "pdfDimensions": {
    "width": 612,    // Original PDF width in points
    "height": 792    // Original PDF height in points
  },
  "annotations": [
    {
      "coordinates": {
        "x": 100,      // PDF coordinates (points)
        "y": 150,      // PDF coordinates (points)
        "width": 200,  // PDF coordinates (points)
        "height": 100  // PDF coordinates (points)
      }
    }
  ]
}
```

### PDF Export Verification
- Annotations appear at exact same positions as in web viewer
- Rectangles maintain aspect ratio and size
- Polygons maintain shape and proportions
- Multi-page annotations appear on correct pages

## Common Issues and Solutions

### Issue: Annotations appear offset in PDF export
**Cause**: Incorrect coordinate conversion
**Solution**: Verify `canvasToPdfCoordinates` scaling factors

### Issue: Annotations change position when zooming
**Cause**: Using screen coordinates instead of canvas coordinates
**Solution**: Ensure all storage uses canvas coordinates

### Issue: Different sizes in PDF vs web viewer
**Cause**: DPI scaling mismatch
**Solution**: Verify 150 DPI rendering vs original PDF dimensions

### Issue: Y-axis flipping in PDF export
**Cause**: PDF coordinate system has origin at bottom-left
**Solution**: Apply Y-flip transformation: `y = height - y`

## Validation Checklist

- [ ] Annotations stay in same visual position across zoom levels
- [ ] JSON export coordinates match expected PDF coordinate system
- [ ] PDF export shows annotations at identical positions
- [ ] Multi-page annotations appear on correct pages
- [ ] Complex polygons maintain shape accuracy
- [ ] Large documents handle coordinates without precision loss
- [ ] Rectangle dimensions are preserved in all formats
- [ ] Polygon point sequences are maintained

## Success Criteria

The coordinate system is working correctly when:
1. **Visual Consistency**: Annotations appear at same positions in web viewer and PDF export
2. **Zoom Independence**: Annotation coordinates don't change with zoom level
3. **Export Accuracy**: JSON coordinates can be used to recreate exact annotations
4. **Scale Preservation**: Annotation sizes and shapes are maintained across formats
5. **Multi-page Support**: Page-specific annotations export correctly

## Debugging Tools

### Browser Console Commands
```javascript
// Check current PDF dimensions
console.log(originalPdfDimensions.current)

// Check canvas dimensions
console.log(canvasRef.current.width, canvasRef.current.height)

// Test coordinate conversion
const testCanvas = {x: 100, y: 100}
const testPdf = canvasToPdfCoordinates(testCanvas.x, testCanvas.y)
console.log('Canvas:', testCanvas, 'PDF:', testPdf)
```

### Visual Verification
1. Create annotation at known position (e.g., corner)
2. Export as PDF
3. Open in PDF viewer with coordinate display
4. Verify coordinates match expected values

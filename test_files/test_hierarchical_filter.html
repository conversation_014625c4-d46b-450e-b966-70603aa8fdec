<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hierarchical Filter Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Hierarchical Room Filter Test</h1>
    
    <div class="test-section">
        <h2>Test 1: CSV Structure Analysis</h2>
        <p>Testing the CSV parsing with sample hierarchical data:</p>
        <div id="test1-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Hierarchy Tree Building</h2>
        <p>Testing the hierarchical tree structure:</p>
        <div id="test2-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Room Filtering</h2>
        <p>Testing room filtering by hierarchy path:</p>
        <div id="test3-result" class="test-result"></div>
    </div>

    <script>
        // Sample CSV data for testing
        const sampleCSV = `Level 1
Level 1,Zone A
Level 1,Zone B
Level 1,Zone E
Level 1,Zone E,BIOWASTE 01.E.28
Level 1,Zone E,CHEMICAL WASTE STORAGE 01.E.24
Level 1,Zone A,OFFICE 01.A.15
Level 1,Zone A,CONFERENCE ROOM 01.A.20
Level 1,Zone B,LABORATORY 01.B.05
Level 1,Zone B,STORAGE 01.B.12
Level 2,Zone A,MEETING ROOM 02.A.08
Level 2,Zone B,RESEARCH LAB 02.B.03
Level 2,Zone C,CLEAN ROOM 02.C.01
Level 2,Zone C,EQUIPMENT ROOM 02.C.07
Level 3,Zone A,EXECUTIVE OFFICE 03.A.01
Level 3,Zone A,RECEPTION 03.A.02
Level 3,Zone B,TRAINING ROOM 03.B.10
Level 3,Zone B,BREAK ROOM 03.B.15`;

        // Simulate the CSV parsing logic
        function analyzeCSVStructure(csvContent) {
            try {
                const lines = csvContent.split('\n').filter(line => line.trim() !== '')
                if (lines.length === 0) return null

                const allRows = lines.map(line => {
                    return line.split(',').map(col => col.trim().replace(/^"|"$/g, ''))
                }).filter(row => row.length > 0)

                if (allRows.length === 0) return null

                const maxColumns = Math.max(...allRows.map(row => row.length))
                
                const columnHeaders = []
                for (let i = 0; i < maxColumns; i++) {
                    columnHeaders.push(`Level ${i + 1}`)
                }

                const hierarchyTree = {}
                const allRoomNames = new Set()

                allRows.forEach(row => {
                    const cleanRow = row.filter(cell => cell && cell.trim() !== '')
                    if (cleanRow.length === 0) return

                    let currentLevel = hierarchyTree
                    let path = []

                    for (let i = 0; i < cleanRow.length; i++) {
                        const value = cleanRow[i].trim()
                        if (!value) continue

                        path.push(value)

                        if (i === cleanRow.length - 1 && cleanRow.length > 1) {
                            if (value.toLowerCase() !== 'nan' && 
                                value.toLowerCase() !== 'null' &&
                                value.toLowerCase() !== 'undefined') {
                                allRoomNames.add(value)
                                
                                if (!currentLevel._rooms) currentLevel._rooms = []
                                currentLevel._rooms.push({
                                    name: value,
                                    path: [...path]
                                })
                            }
                        } else {
                            if (!currentLevel[value]) {
                                currentLevel[value] = {}
                            }
                            currentLevel = currentLevel[value]
                        }
                    }
                })

                return {
                    columnHeaders,
                    hierarchyTree,
                    maxDepth: maxColumns,
                    totalRooms: allRoomNames.size,
                    allRoomNames: Array.from(allRoomNames).sort()
                }
            } catch (error) {
                console.error('Error analyzing CSV structure:', error)
                return null
            }
        }

        // Run tests
        function runTests() {
            // Test 1: CSV Structure Analysis
            try {
                const structure = analyzeCSVStructure(sampleCSV)
                document.getElementById('test1-result').className = 'test-result success'
                document.getElementById('test1-result').textContent = 
                    `✓ CSV parsed successfully!\n` +
                    `Max Depth: ${structure.maxDepth}\n` +
                    `Total Rooms: ${structure.totalRooms}\n` +
                    `Column Headers: ${structure.columnHeaders.join(', ')}\n` +
                    `Sample Room Names: ${structure.allRoomNames.slice(0, 5).join(', ')}...`
            } catch (error) {
                document.getElementById('test1-result').className = 'test-result error'
                document.getElementById('test1-result').textContent = `✗ Error: ${error.message}`
            }

            // Test 2: Hierarchy Tree Building
            try {
                const structure = analyzeCSVStructure(sampleCSV)
                const levels = Object.keys(structure.hierarchyTree)
                const level1Zones = Object.keys(structure.hierarchyTree['Level 1'] || {}).filter(k => k !== '_rooms')
                
                document.getElementById('test2-result').className = 'test-result success'
                document.getElementById('test2-result').textContent = 
                    `✓ Hierarchy tree built successfully!\n` +
                    `Top Level Options: ${levels.join(', ')}\n` +
                    `Level 1 Zones: ${level1Zones.join(', ')}\n` +
                    `Level 1 → Zone A rooms: ${structure.hierarchyTree['Level 1']['Zone A']._rooms?.length || 0}`
            } catch (error) {
                document.getElementById('test2-result').className = 'test-result error'
                document.getElementById('test2-result').textContent = `✗ Error: ${error.message}`
            }

            // Test 3: Room Filtering
            try {
                const structure = analyzeCSVStructure(sampleCSV)
                const level1ZoneARooms = structure.hierarchyTree['Level 1']['Zone A']._rooms || []
                const roomNames = level1ZoneARooms.map(room => room.name)
                
                document.getElementById('test3-result').className = 'test-result success'
                document.getElementById('test3-result').textContent = 
                    `✓ Room filtering works!\n` +
                    `Rooms in Level 1 → Zone A: ${roomNames.join(', ')}\n` +
                    `Sample room path: ${level1ZoneARooms[0]?.path.join(' → ') || 'N/A'}`
            } catch (error) {
                document.getElementById('test3-result').className = 'test-result error'
                document.getElementById('test3-result').textContent = `✗ Error: ${error.message}`
            }
        }

        // Run tests when page loads
        window.onload = runTests
    </script>
</body>
</html>

# Testing Annotated PDF Export Feature

## Test Steps

### 1. Basic Functionality Test
1. Upload the test PDF file (`test_document.pdf`)
2. Create several annotations:
   - Add 2-3 rectangle annotations on page 1
   - Add 1-2 polygon annotations on page 1
   - Navigate to page 2 and add more annotations
3. Click the 📄✏️ button to export annotated PDF
4. Verify the download starts and file is saved as `annotated_document.pdf`

### 2. Visual Verification
1. Open the exported `annotated_document.pdf` in a PDF viewer
2. Verify that:
   - All rectangle annotations appear as red outlined boxes
   - All polygon annotations appear as connected red lines
   - Annotations are positioned correctly on the PDF
   - Original PDF content is preserved
   - Multi-page annotations are on correct pages

### 3. Coordinate Accuracy Test
1. Create annotations at specific locations (corners, center, etc.)
2. Export annotated PDF
3. Compare annotation positions in exported PDF with original canvas positions
4. Verify coordinates are accurate despite zoom level changes

### 4. Edge Cases
1. Test with empty annotations (button should be disabled)
2. Test with no PDF loaded (button should be disabled)
3. Test with very small and very large annotations
4. Test with polygons having many points (10+ vertices)

## Expected Results

### Successful Export
- ✅ Download dialog appears
- ✅ File saves as `annotated_document.pdf`
- ✅ File size is reasonable (similar to original + small overhead)
- ✅ No JavaScript errors in console

### Visual Quality
- ✅ Annotations are clearly visible
- ✅ Red color is used for all annotations
- ✅ Line thickness is appropriate (2px)
- ✅ Original PDF quality is maintained

### Positioning Accuracy
- ✅ Rectangles match canvas positions
- ✅ Polygons match canvas shapes
- ✅ Multi-page annotations are on correct pages
- ✅ Zoom level doesn't affect export accuracy

## Known Limitations

1. **Color Options**: Currently only red annotations (can be enhanced)
2. **Line Styles**: Solid lines only (no dashed/dotted options)
3. **Fill Options**: Outline only (no filled shapes)
4. **Text Annotations**: Not supported (only shapes)

## Troubleshooting

### If Export Fails
1. Check browser console for errors
2. Verify PDF file is properly loaded
3. Ensure annotations exist
4. Try with a smaller/simpler PDF

### If Positioning is Off
1. Verify original PDF dimensions are captured correctly
2. Check coordinate conversion logic
3. Test with different zoom levels during annotation creation

### If PDF is Corrupted
1. Verify pdf-lib dependency is properly installed
2. Check for memory issues with large PDFs
3. Test with different PDF versions/formats

## Success Criteria

The feature is working correctly if:
- ✅ Export button is only enabled when PDF and annotations exist
- ✅ Exported PDF opens without errors
- ✅ All annotations are visible and positioned correctly
- ✅ Original PDF content is preserved
- ✅ File downloads with appropriate filename
- ✅ Process completes without JavaScript errors

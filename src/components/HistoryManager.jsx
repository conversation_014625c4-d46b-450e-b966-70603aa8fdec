import { useState, useCallback, useRef } from 'react'

/**
 * Custom hook for managing undo/redo history for bounding box operations
 * Maintains separate undo and redo stacks with a maximum of 5 operations each
 */
export const useHistoryManager = () => {
  const [undoStack, setUndoStack] = useState([])
  const [redoStack, setRedoStack] = useState([])
  const isPerformingHistoryOperation = useRef(false)

  /**
   * Save the current state to history before making changes
   * @param {Object} currentState - The current annotation state to save
   * @param {string} operation - Description of the operation being performed
   * @param {string} pdfPageKey - Key identifying the current PDF page (e.g., "0-1")
   */
  const saveToHistory = useCallback((currentState, operation, pdfPageKey) => {
    // Don't save to history if we're currently performing an undo/redo operation
    if (isPerformingHistoryOperation.current) {
      return
    }

    const historyEntry = {
      state: JSON.parse(JSON.stringify(currentState)), // Deep clone to prevent mutations
      operation,
      pdfPageKey,
      timestamp: Date.now()
    }

    setUndoStack(prev => {
      const newStack = [...prev, historyEntry]
      // Keep only the last 5 entries
      return newStack.length > 5 ? newStack.slice(-5) : newStack
    })

    // Clear redo stack when a new operation is performed
    setRedoStack([])
  }, [])

  /**
   * Undo the last operation
   * @param {Function} getCurrentState - Function to get current annotation state
   * @param {Function} restoreState - Function to restore annotation state
   * @param {string} currentPdfPageKey - Current PDF page key
   * @returns {boolean} - True if undo was performed, false if no undo available
   */
  const undo = useCallback((getCurrentState, restoreState, currentPdfPageKey) => {
    if (undoStack.length === 0) {
      return false
    }

    const lastEntry = undoStack[undoStack.length - 1]
    
    // Only undo operations from the current page
    if (lastEntry.pdfPageKey !== currentPdfPageKey) {
      return false
    }

    isPerformingHistoryOperation.current = true

    try {
      // Save current state to redo stack before undoing
      const currentState = getCurrentState()
      const redoEntry = {
        state: JSON.parse(JSON.stringify(currentState)),
        operation: `Redo: ${lastEntry.operation}`,
        pdfPageKey: currentPdfPageKey,
        timestamp: Date.now()
      }

      setRedoStack(prev => {
        const newStack = [...prev, redoEntry]
        // Keep only the last 5 entries
        return newStack.length > 5 ? newStack.slice(-5) : newStack
      })

      // Restore the previous state
      restoreState(lastEntry.state)

      // Remove the last entry from undo stack
      setUndoStack(prev => prev.slice(0, -1))

      return true
    } finally {
      isPerformingHistoryOperation.current = false
    }
  }, [undoStack])

  /**
   * Redo the last undone operation
   * @param {Function} getCurrentState - Function to get current annotation state
   * @param {Function} restoreState - Function to restore annotation state
   * @param {string} currentPdfPageKey - Current PDF page key
   * @returns {boolean} - True if redo was performed, false if no redo available
   */
  const redo = useCallback((getCurrentState, restoreState, currentPdfPageKey) => {
    if (redoStack.length === 0) {
      return false
    }

    const lastRedoEntry = redoStack[redoStack.length - 1]
    
    // Only redo operations from the current page
    if (lastRedoEntry.pdfPageKey !== currentPdfPageKey) {
      return false
    }

    isPerformingHistoryOperation.current = true

    try {
      // Save current state to undo stack before redoing
      const currentState = getCurrentState()
      const undoEntry = {
        state: JSON.parse(JSON.stringify(currentState)),
        operation: lastRedoEntry.operation.replace('Redo: ', ''),
        pdfPageKey: currentPdfPageKey,
        timestamp: Date.now()
      }

      setUndoStack(prev => {
        const newStack = [...prev, undoEntry]
        // Keep only the last 5 entries
        return newStack.length > 5 ? newStack.slice(-5) : newStack
      })

      // Restore the redo state
      restoreState(lastRedoEntry.state)

      // Remove the last entry from redo stack
      setRedoStack(prev => prev.slice(0, -1))

      return true
    } finally {
      isPerformingHistoryOperation.current = false
    }
  }, [redoStack])

  /**
   * Clear all history (useful when switching pages or PDFs)
   */
  const clearHistory = useCallback(() => {
    setUndoStack([])
    setRedoStack([])
  }, [])

  /**
   * Clear history for a specific PDF page
   * @param {string} pdfPageKey - The PDF page key to clear history for
   */
  const clearHistoryForPage = useCallback((pdfPageKey) => {
    setUndoStack(prev => prev.filter(entry => entry.pdfPageKey !== pdfPageKey))
    setRedoStack(prev => prev.filter(entry => entry.pdfPageKey !== pdfPageKey))
  }, [])

  /**
   * Check if undo is available for the current page
   * @param {string} currentPdfPageKey - Current PDF page key
   * @returns {boolean}
   */
  const canUndo = useCallback((currentPdfPageKey) => {
    return undoStack.some(entry => entry.pdfPageKey === currentPdfPageKey)
  }, [undoStack])

  /**
   * Check if redo is available for the current page
   * @param {string} currentPdfPageKey - Current PDF page key
   * @returns {boolean}
   */
  const canRedo = useCallback((currentPdfPageKey) => {
    return redoStack.some(entry => entry.pdfPageKey === currentPdfPageKey)
  }, [redoStack])

  /**
   * Get the description of the last operation that can be undone
   * @param {string} currentPdfPageKey - Current PDF page key
   * @returns {string|null}
   */
  const getLastUndoOperation = useCallback((currentPdfPageKey) => {
    const lastEntry = undoStack
      .filter(entry => entry.pdfPageKey === currentPdfPageKey)
      .slice(-1)[0]
    return lastEntry ? lastEntry.operation : null
  }, [undoStack])

  /**
   * Get the description of the last operation that can be redone
   * @param {string} currentPdfPageKey - Current PDF page key
   * @returns {string|null}
   */
  const getLastRedoOperation = useCallback((currentPdfPageKey) => {
    const lastEntry = redoStack
      .filter(entry => entry.pdfPageKey === currentPdfPageKey)
      .slice(-1)[0]
    return lastEntry ? lastEntry.operation : null
  }, [redoStack])

  return {
    // Core operations
    saveToHistory,
    undo,
    redo,
    clearHistory,
    clearHistoryForPage,
    
    // Status checks
    canUndo,
    canRedo,
    getLastUndoOperation,
    getLastRedoOperation,
    
    // Debug info (can be removed in production)
    undoStackSize: undoStack.length,
    redoStackSize: redoStack.length
  }
}

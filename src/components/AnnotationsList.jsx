import React, { useState, useMemo } from 'react'

const AnnotationsList = ({
  annotations,
  selectedAnnotations,
  onSelectAnnotation,
  onCopyAnnotation,
  onDeleteAnnotation,
  onUpdateAnnotationLabel,
  onCheckForOverlaps,
  isCollapsed = false,
  onToggleCollapse,
  // Progress bar props
  csvFileName = null,
  totalRoomNames = [],
  isFilterActive = false,
  filteredRoomNames = []
}) => {
  const [editingLabelId, setEditingLabelId] = useState(null)
  const [editingLabelValue, setEditingLabelValue] = useState('')

  // Always render the component to show the toggle button
  const hasAnnotations = annotations && annotations.length > 0

  // Calculate progress bar data
  const progressData = useMemo(() => {
    // Only show progress bar if CSV is uploaded
    if (!csvFileName || (!totalRoomNames.length && !filteredRoomNames.length)) {
      return null
    }

    // Determine which room names to use based on filter status
    const relevantRoomNames = isFilterActive ? filteredRoomNames : totalRoomNames
    const totalRooms = relevantRoomNames.length

    if (totalRooms === 0) {
      return null
    }

    // Get unique room names that have been annotated
    const annotatedRoomNames = new Set()
    annotations.forEach(annotation => {
      // Check both roomName and label properties for room assignment
      const assignedRoom = annotation.roomName || annotation.label
      if (assignedRoom && relevantRoomNames.includes(assignedRoom)) {
        annotatedRoomNames.add(assignedRoom)
      }
    })

    const annotatedCount = annotatedRoomNames.size
    const percentage = totalRooms > 0 ? Math.round((annotatedCount / totalRooms) * 100) : 0

    return {
      annotatedCount,
      totalRooms,
      percentage,
      isFiltered: isFilterActive,
      remainingRooms: totalRooms - annotatedCount
    }
  }, [annotations, csvFileName, totalRoomNames, filteredRoomNames, isFilterActive])

  // Check which annotations have overlaps
  const getOverlappingAnnotations = (annotation) => {
    if (onCheckForOverlaps) {
      return onCheckForOverlaps(annotation)
    }
    return []
  }

  const handleLabelClick = (annotation, e) => {
    e.stopPropagation()
    setEditingLabelId(annotation.id)
    setEditingLabelValue(annotation.label || `${annotation.type} ${annotation.id.slice(0, 8)}`)
  }

  const handleLabelSubmit = (annotationId) => {
    if (editingLabelValue.trim()) {
      onUpdateAnnotationLabel(annotationId, editingLabelValue.trim())
    }
    setEditingLabelId(null)
    setEditingLabelValue('')
  }

  const handleLabelKeyDown = (e, annotationId) => {
    // Prevent all keyboard shortcuts from interfering with label editing
    e.stopPropagation()

    if (e.key === 'Enter') {
      e.preventDefault()
      handleLabelSubmit(annotationId)
    } else if (e.key === 'Escape') {
      e.preventDefault()
      setEditingLabelId(null)
      setEditingLabelValue('')
    }
  }

  return (
    <div className={`annotations-list ${isCollapsed ? 'collapsed' : ''}`}>
      {/* Toggle button */}
      <div
        className="annotations-panel-toggle"
        onClick={onToggleCollapse}
        title={isCollapsed ? 'Show Annotations Panel' : 'Hide Annotations Panel'}
      >
        <i className="fas fa-chevron-right"></i>
      </div>

      {!isCollapsed && (
        <>
          <h3>Annotations ({hasAnnotations ? annotations.length : 0})</h3>

          {/* Progress Bar */}
          {progressData && (
            <div className="annotation-progress-section">
              <div className="progress-header">
                <span className="progress-text">
                  Room Annotation Progress{progressData.isFiltered ? ' (Filtered)' : ''}
                </span>
                <span className="progress-stats">
                  {progressData.annotatedCount} / {progressData.totalRooms} ({progressData.percentage}%)
                </span>
              </div>
              <div className="progress-bar-container">
                <div
                  className="progress-bar-fill"
                  data-percentage={progressData.percentage}
                  style={{ width: `${progressData.percentage}%` }}
                  title={`${progressData.annotatedCount} out of ${progressData.totalRooms} rooms annotated${progressData.isFiltered ? ' (filtered view)' : ''}`}
                />
              </div>
            </div>
          )}
          {hasAnnotations ? (
            <div className="annotation-items">
        {annotations.map(annotation => {
          const overlappingAnnotations = getOverlappingAnnotations(annotation)
          const hasOverlaps = overlappingAnnotations.length > 0

          return (
            <div
              key={annotation.id}
              className={`annotation-item ${selectedAnnotations && selectedAnnotations.some(selected => selected.id === annotation.id) ? 'selected' : ''} ${hasOverlaps ? 'overlap-warning' : ''}`}
              onClick={() => onSelectAnnotation(annotation)}
              title={hasOverlaps ? `Warning: Overlaps with ${overlappingAnnotations.map(ann => ann.label || ann.type + ' ' + ann.id.slice(0, 8)).join(', ')}` : ''}
            >
              {editingLabelId === annotation.id ? (
                <input
                  type="text"
                  className="annotation-label-input"
                  data-editing-label="true"
                  value={editingLabelValue}
                  onChange={(e) => {
                    e.stopPropagation()
                    setEditingLabelValue(e.target.value)
                  }}
                  onBlur={() => handleLabelSubmit(annotation.id)}
                  onKeyDown={(e) => handleLabelKeyDown(e, annotation.id)}
                  onKeyUp={(e) => e.stopPropagation()}
                  onKeyPress={(e) => e.stopPropagation()}
                  autoFocus
                  onClick={(e) => e.stopPropagation()}
                />
              ) : (
                <div
                  className="annotation-label"
                  onClick={(e) => handleLabelClick(annotation, e)}
                  title="Click to edit label"
                >
                  {annotation.label || `${annotation.type} ${annotation.id.slice(0, 8)}`}
                </div>
              )}
              <div className="annotation-details">
                {annotation.type === 'rectangle'
                  ? `Rectangle (${Math.round(annotation.x)}, ${Math.round(annotation.y)}, ${Math.round(annotation.width)}×${Math.round(annotation.height)})`
                  : `Polygon (${annotation.points.length} points)`
                }
                {hasOverlaps && (
                  <div className="overlap-warning-text">
                    <i className="fas fa-exclamation-triangle"></i>
                    Overlaps detected
                  </div>
                )}
              </div>
              <div className="annotation-controls">
                <button
                  className="annotation-control-button"
                  onClick={(e) => {
                    e.stopPropagation()
                    onCopyAnnotation(annotation)
                  }}
                >
                  Copy
                </button>
                <button
                  className="annotation-control-button"
                  onClick={(e) => {
                    e.stopPropagation()
                    onDeleteAnnotation(annotation.id)
                  }}
                >
                  Delete
                </button>
              </div>
            </div>
          )
            })}
            </div>
          ) : (
            <div className="no-annotations">
              <p>No annotations yet. Use the tools above to create annotations.</p>
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default AnnotationsList

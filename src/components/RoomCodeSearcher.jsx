import { useState, useCallback, useEffect } from 'react'
import { usePDFTextExtractor } from './PDFTextExtractor'

export const useRoomCodeSearcher = (allPdfData, roomCodes, pdfToCanvasCoordinates, currentPdfIndex) => {
  const [searchStatus, setSearchStatus] = useState('idle') // 'idle', 'searching', 'completed', 'error'
  const [searchResults, setSearchResults] = useState(null)
  const [lastSearchTime, setLastSearchTime] = useState(null)
  
  const {
    isSearching,
    searchProgress,
    roomCodeCache,
    searchRoomCodesInPDFs,
    abortSearch,
    getRoomCodeCoordinates,
    clearCache
  } = usePDFTextExtractor(pdfToCanvasCoordinates)

  // Automatically trigger search when both PDFs and room codes are available
  const triggerAutoSearch = useCallback(async () => {
    if (!allPdfData || allPdfData.length === 0 || !roomCodes || roomCodes.length === 0 || currentPdfIndex === null) {
      return
    }

    // Only search the current PDF
    const currentPdfData = allPdfData[currentPdfIndex]
    if (!currentPdfData) {
      console.log('No current PDF data available')
      return
    }

    console.log(`Auto-triggering room code search for current PDF: ${currentPdfData.name}`)
    setSearchStatus('searching')

    try {
      // Search only the current PDF by passing an array with just the current PDF
      const results = await searchRoomCodesInPDFs(
        [currentPdfData],
        roomCodes,
        (current, total, foundInPage) => {
          // Progress callback
          console.log(`Search progress: ${current}/${total} pages processed, found ${foundInPage} codes in current page`)
        },
        currentPdfIndex // Pass the actual PDF index for coordinate conversion
      )
      
      setSearchResults({
        totalCodes: roomCodes.length,
        foundCodes: results.size,
        foundCodesMap: results,
        searchTime: new Date(),
        searchedPdfName: currentPdfData.name,
        searchedPdfIndex: currentPdfIndex
      })
      
      setSearchStatus('completed')
      setLastSearchTime(new Date())
      
      console.log(`Room code search completed for current PDF: ${results.size}/${roomCodes.length} codes found`)

    } catch (error) {
      console.error('Room code search failed:', error)
      setSearchStatus('error')
    }
  }, [allPdfData, roomCodes, searchRoomCodesInPDFs, currentPdfIndex])

  // Manual search trigger
  const startSearch = useCallback(async () => {
    if (isSearching) {
      console.log('Search already in progress')
      return
    }
    
    await triggerAutoSearch()
  }, [triggerAutoSearch, isSearching])

  // Stop ongoing search
  const stopSearch = useCallback(() => {
    abortSearch()
    setSearchStatus('idle')
  }, [abortSearch])

  // Reset search state
  const resetSearch = useCallback(() => {
    clearCache()
    setSearchStatus('idle')
    setSearchResults(null)
    setLastSearchTime(null)
  }, [clearCache])

  // Clear cache when switching PDFs (called automatically)
  const clearCacheForPdfSwitch = useCallback(() => {
    clearCache()
    setSearchStatus('idle')
    // Keep searchResults and lastSearchTime to track what was last searched
  }, [clearCache])

  // Get search statistics
  const getSearchStats = useCallback(() => {
    if (!searchResults) {
      return {
        totalCodes: roomCodes?.length || 0,
        foundCodes: 0,
        foundPercentage: 0,
        notFoundCodes: roomCodes?.length || 0
      }
    }
    
    const totalCodes = searchResults.totalCodes
    const foundCodes = searchResults.foundCodes
    const foundPercentage = totalCodes > 0 ? Math.round((foundCodes / totalCodes) * 100) : 0
    
    return {
      totalCodes,
      foundCodes,
      foundPercentage,
      notFoundCodes: totalCodes - foundCodes
    }
  }, [searchResults, roomCodes])

  // Get list of found room codes
  const getFoundRoomCodes = useCallback(() => {
    if (!searchResults) return []
    
    return Array.from(searchResults.foundCodesMap.values())
  }, [searchResults])

  // Get list of not found room codes
  const getNotFoundRoomCodes = useCallback(() => {
    if (!searchResults || !roomCodes) return []
    
    const foundCodes = new Set(searchResults.foundCodesMap.keys())
    return roomCodes.filter(({ code }) => !foundCodes.has(code))
  }, [searchResults, roomCodes])

  // Check if a specific room code was found
  const isRoomCodeFound = useCallback((roomCode) => {
    return roomCodeCache.has(roomCode)
  }, [roomCodeCache])

  // Get detailed information about a room code including all matches
  const getRoomCodeDetails = useCallback((roomCode) => {
    const codeData = roomCodeCache.get(roomCode)
    if (!codeData) return null

    return {
      code: roomCode,
      roomName: codeData.roomName,
      found: codeData.found,
      matchCount: codeData.matchCount || 1,
      hasMultipleMatches: codeData.allMatches && codeData.allMatches.length > 1,
      primaryCoordinates: codeData.coordinates,
      allMatches: codeData.allMatches || [
        {
          coordinates: codeData.coordinates,
          pdfCoordinates: codeData.pdfCoordinates,
          textItem: codeData.textItem,
          pdfIndex: codeData.pdfIndex,
          pageIndex: codeData.pageIndex
        }
      ]
    }
  }, [roomCodeCache])

  // Get statistics about multiple matches
  const getMultipleMatchStats = useCallback(() => {
    const stats = {
      totalRoomCodes: roomCodeCache.size,
      roomCodesWithMultipleMatches: 0,
      totalMatches: 0,
      averageMatchesPerCode: 0
    }

    roomCodeCache.forEach((codeData) => {
      const matchCount = codeData.matchCount || 1
      stats.totalMatches += matchCount

      if (matchCount > 1) {
        stats.roomCodesWithMultipleMatches++
      }
    })

    stats.averageMatchesPerCode = stats.totalRoomCodes > 0
      ? Math.round((stats.totalMatches / stats.totalRoomCodes) * 10) / 10
      : 0

    return stats
  }, [roomCodeCache])

  // Create annotation at room code location
  const createAnnotationAtRoomCode = useCallback((roomCode, annotationCreator, userClickPosition = null) => {
    const codeData = roomCodeCache.get(roomCode)
    if (!codeData || !annotationCreator) {
      console.warn(`Cannot create annotation: room code "${roomCode}" not found or no annotation creator provided`)
      return null
    }

    let selectedMatch = null
    let coordinates, pdfIndex, pageIndex

    // If there are multiple matches and user provided a click position, find the closest match
    if (codeData.allMatches && codeData.allMatches.length > 1 && userClickPosition) {
      let minDistance = Infinity
      codeData.allMatches.forEach(match => {
        const distance = Math.sqrt(
          Math.pow(match.coordinates.x - userClickPosition.x, 2) +
          Math.pow(match.coordinates.y - userClickPosition.y, 2)
        )
        if (distance < minDistance) {
          minDistance = distance
          selectedMatch = match
        }
      })

      coordinates = selectedMatch.coordinates
      pdfIndex = selectedMatch.pdfIndex
      pageIndex = selectedMatch.pageIndex

      console.log(`Selected closest match for "${roomCode}" at distance ${Math.round(minDistance)}px from click position`)
    } else {
      // Use primary coordinates (first match or single match)
      coordinates = codeData.coordinates
      pdfIndex = codeData.pdfIndex
      pageIndex = codeData.pageIndex
      selectedMatch = {
        coordinates: coordinates,
        pdfCoordinates: codeData.pdfCoordinates,
        textItem: codeData.textItem,
        pdfIndex: pdfIndex,
        pageIndex: pageIndex
      }
    }

    // Create a small rectangle annotation at the selected room code location
    const annotation = {
      id: `room-code-${roomCode}-${Date.now()}`,
      type: 'rectangle',
      x: coordinates.x - 5, // Small padding around the text
      y: coordinates.y - 5,
      width: Math.max(coordinates.width + 10, 50), // Minimum width
      height: Math.max(coordinates.height + 10, 20), // Minimum height
      color: '#ff6b6b', // Distinctive color for room code annotations
      label: roomCode,
      roomName: codeData.roomName,
      roomPath: [codeData.roomName], // Simple path for now
      pdfIndex: pdfIndex,
      pageIndex: pageIndex,
      isRoomCodeAnnotation: true, // Flag to identify these special annotations
      selectedMatch: selectedMatch, // Store which match was selected
      totalMatches: codeData.allMatches ? codeData.allMatches.length : 1
    }

    // Use the provided annotation creator function
    return annotationCreator(annotation)
  }, [roomCodeCache])

  // Effect to trigger auto-search when conditions are met or when PDF is switched
  useEffect(() => {
    // Auto-search if we have both PDFs and room codes, and either:
    // 1. No search has been done yet, OR
    // 2. The current PDF has changed (different from last search)
    if (allPdfData && allPdfData.length > 0 &&
        roomCodes && roomCodes.length > 0 &&
        currentPdfIndex !== null &&
        searchStatus === 'idle') {

      const currentPdfData = allPdfData[currentPdfIndex]
      const shouldSearch = !lastSearchTime || // No search done yet
                          !searchResults || // No results yet
                          !searchResults.searchedPdfName || // No PDF name recorded
                          searchResults.searchedPdfName !== currentPdfData?.name // Different PDF

      if (shouldSearch) {
        console.log(`Conditions met for auto-search: Current PDF "${currentPdfData?.name}" needs to be searched`)
        // Small delay to ensure UI is ready
        setTimeout(() => {
          triggerAutoSearch()
        }, 1000)
      }
    }
  }, [allPdfData, roomCodes, searchStatus, lastSearchTime, triggerAutoSearch, currentPdfIndex, searchResults])

  // Effect to clear cache when PDF is switched
  useEffect(() => {
    if (searchResults && searchResults.searchedPdfIndex !== null &&
        currentPdfIndex !== null &&
        searchResults.searchedPdfIndex !== currentPdfIndex) {
      console.log(`PDF switched from index ${searchResults.searchedPdfIndex} to ${currentPdfIndex}, clearing cache`)
      clearCacheForPdfSwitch()
    }
  }, [currentPdfIndex, searchResults, clearCacheForPdfSwitch])

  return {
    // State
    searchStatus,
    isSearching,
    searchProgress,
    searchResults,
    lastSearchTime,
    roomCodeCache,
    
    // Actions
    startSearch,
    stopSearch,
    resetSearch,
    
    // Data access
    getRoomCodeCoordinates,
    getSearchStats,
    getFoundRoomCodes,
    getNotFoundRoomCodes,
    isRoomCodeFound,
    createAnnotationAtRoomCode,

    // Multiple match support
    getRoomCodeDetails,
    getMultipleMatchStats
  }
}

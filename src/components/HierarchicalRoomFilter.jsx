import React, { useState, useEffect, useCallback } from 'react'

const HierarchicalRoomFilter = ({
  csvStructure,
  hierarchicalData,
  onRoomSelection,
  onFilterChange,
  className = ''
}) => {
  const [selectedPath, setSelectedPath] = useState([])
  const [availableOptions, setAvailableOptions] = useState([])
  const [filteredRooms, setFilteredRooms] = useState([])

  // Initialize available options for the first level
  useEffect(() => {
    if (!hierarchicalData || !csvStructure) {
      setAvailableOptions([])
      setFilteredRooms([])
      return
    }

    // Get first level options
    const firstLevelOptions = Object.keys(hierarchicalData).filter(key => key !== '_rooms')
    setAvailableOptions([firstLevelOptions])
    setSelectedPath([])
    setFilteredRooms([])
  }, [hierarchicalData, csvStructure])

  // Get all rooms under a path (including sub-levels)
  const getAllRoomsUnderPath = useCallback((path) => {
    if (!hierarchicalData || path.length === 0) return []

    let currentLevel = hierarchicalData

    // Navigate to the selected level
    for (const pathItem of path) {
      if (currentLevel[pathItem]) {
        currentLevel = currentLevel[pathItem]
      } else {
        return []
      }
    }

    // Recursively collect all rooms from this level and sub-levels
    const collectRooms = (level) => {
      let rooms = []

      // Add rooms at current level
      if (level._rooms) {
        rooms.push(...level._rooms)
      }

      // Add rooms from sub-levels
      Object.keys(level).forEach(key => {
        if (key !== '_rooms' && typeof level[key] === 'object') {
          rooms.push(...collectRooms(level[key]))
        }
      })

      return rooms
    }

    return collectRooms(currentLevel)
  }, [hierarchicalData])

  // Update available options when selection changes
  const updateAvailableOptions = useCallback((newPath) => {
    if (!hierarchicalData || !csvStructure) return

    const newOptions = []
    let currentLevel = hierarchicalData

    // Build options for each level up to the maximum depth
    for (let i = 0; i < csvStructure.maxDepth - 1; i++) { // -1 because last level is room names
      if (i < newPath.length) {
        // Navigate to the selected level
        if (currentLevel[newPath[i]]) {
          currentLevel = currentLevel[newPath[i]]
        } else {
          break
        }
      }

      if (i === newPath.length) {
        // Get options for the next level
        const options = Object.keys(currentLevel).filter(key => key !== '_rooms')
        if (options.length > 0) {
          newOptions.push(options)
        }
        break // Only show the next available level
      }
    }

    setAvailableOptions(newOptions)

    // Get all rooms under the current path (including sub-levels)
    const allRooms = getAllRoomsUnderPath(newPath)
    setFilteredRooms(allRooms)

    // Notify parent component of filter change
    if (onFilterChange) {
      onFilterChange({
        selectedPath: newPath,
        filteredRooms: allRooms,
        availableRooms: allRooms.map(room => room.name)
      })
    }
  }, [hierarchicalData, csvStructure, onFilterChange, getAllRoomsUnderPath])

  // Handle selection at a specific level
  const handleLevelSelection = useCallback((levelIndex, value) => {
    const newPath = [...selectedPath.slice(0, levelIndex), value]
    setSelectedPath(newPath)
    updateAvailableOptions(newPath)
  }, [selectedPath, updateAvailableOptions])

  // Handle room selection
  const handleRoomSelection = useCallback((roomName) => {
    if (onRoomSelection) {
      const selectedRoom = filteredRooms.find(room => room.name === roomName)
      onRoomSelection(roomName, selectedRoom?.path || [...selectedPath, roomName])
    }
  }, [filteredRooms, selectedPath, onRoomSelection])

  // Clear all selections
  const clearSelections = useCallback(() => {
    setSelectedPath([])
    updateAvailableOptions([])
  }, [updateAvailableOptions])

  if (!csvStructure || !hierarchicalData) {
    return (
      <div className={`hierarchical-room-filter ${className}`}>
        <div className="filter-message">
          No hierarchical data available. Please upload a CSV file with hierarchical structure.
        </div>
      </div>
    )
  }

  return (
    <div className={`hierarchical-room-filter ${className}`}>
      <div className="filter-header">
        <h4>Filter Rooms by Hierarchy</h4>
        {selectedPath.length > 0 && (
          <button 
            className="clear-filters-btn"
            onClick={clearSelections}
            title="Clear all filters"
          >
            Clear Filters
          </button>
        )}
      </div>

      <div className="filter-levels">
        {availableOptions.map((options, levelIndex) => (
          <div key={levelIndex} className="filter-level">
            <label className="filter-level-label">
              {csvStructure.columnHeaders[levelIndex] || `Level ${levelIndex + 1}`}:
            </label>
            <select
              className="filter-level-select"
              value={selectedPath[levelIndex] || ''}
              onChange={(e) => handleLevelSelection(levelIndex, e.target.value)}
            >
              <option value="">-- Select {csvStructure.columnHeaders[levelIndex] || `Level ${levelIndex + 1}`} --</option>
              {options.map(option => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>

      {selectedPath.length > 0 && (
        <div className="filter-summary">
          <div className="selected-path">
            <strong>Selected Path:</strong> {selectedPath.join(' → ')}
          </div>
          <div className="filter-stats">
            <span className="rooms-count">{filteredRooms.length} rooms available</span>
            {availableOptions.length > 0 && (
              <span className="continue-hint">
                • Continue selecting sub-levels or apply filter now
              </span>
            )}
          </div>
        </div>
      )}

      {selectedPath.length > 0 && (
        <div className="filter-actions">
          <button
            className="apply-filter-btn"
            onClick={() => {
              if (onFilterChange) {
                onFilterChange({
                  selectedPath,
                  filteredRooms,
                  availableRooms: filteredRooms.map(room => room.name),
                  applied: true
                })
              }
            }}
            title={`Apply filter with ${filteredRooms.length} rooms`}
          >
            Apply Filter ({filteredRooms.length} rooms)
          </button>
          {availableOptions.length > 0 && (
            <span className="continue-text">
              or continue selecting sub-levels →
            </span>
          )}
        </div>
      )}

      {onRoomSelection && filteredRooms.length > 0 && (
        <div className="filtered-rooms">
          <h5>Or Select Specific Room:</h5>
          <div className="rooms-list">
            {filteredRooms.slice(0, 10).map((room, index) => (
              <button
                key={index}
                className="room-item"
                onClick={() => handleRoomSelection(room.name)}
                title={`Full path: ${room.path.join(' → ')}`}
              >
                {room.name}
              </button>
            ))}
            {filteredRooms.length > 10 && (
              <div className="more-rooms-hint">
                ... and {filteredRooms.length - 10} more rooms
              </div>
            )}
          </div>
        </div>
      )}

      {selectedPath.length > 0 && filteredRooms.length === 0 && (
        <div className="no-rooms-message">
          No rooms found for the selected path. Try selecting different options.
        </div>
      )}
    </div>
  )
}

export default HierarchicalRoomFilter

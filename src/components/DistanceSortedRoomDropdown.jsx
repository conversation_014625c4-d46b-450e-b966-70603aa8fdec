import React, { useState, useEffect, useRef, useMemo } from 'react'
import { 
  sortRoomNamesByDistance, 
  formatDistance, 
  getDistanceIndicator,
  getCloseRooms 
} from './DistanceCalculator'

const DistanceSortedRoomDropdown = ({
  roomNames,
  position,
  onSelectRoom,
  onCancel,
  isVisible,
  currentAnnotations = [],
  annotation, // The annotation that was just created
  roomCodeCache, // Map of room codes to coordinates
  extractRoomCode // Function to extract room code from room name
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [customRoomName, setCustomRoomName] = useState('')
  const [showCustomInput, setShowCustomInput] = useState(false)
  const [showDistanceInfo, setShowDistanceInfo] = useState(false)
  const dropdownRef = useRef(null)
  const searchInputRef = useRef(null)
  const customInputRef = useRef(null)

  // Get used room names from current annotations
  const usedRoomNames = new Set(
    currentAnnotations
      .filter(ann => ann.roomName || ann.label)
      .map(ann => ann.roomName || ann.label)
  )

  // Sort rooms by distance from annotation centroid
  const sortedRoomsWithDistance = useMemo(() => {
    if (!annotation || !roomCodeCache || !extractRoomCode) {
      // Fallback to original room names without distance sorting
      return roomNames.map(roomName => ({
        roomName,
        distance: Infinity,
        hasCoordinates: false,
        coordinates: null
      }))
    }

    return sortRoomNamesByDistance(roomNames, annotation, roomCodeCache, extractRoomCode)
  }, [roomNames, annotation, roomCodeCache, extractRoomCode])

  // Filter rooms based on search term and exclude already used names
  const filteredRooms = useMemo(() => {
    return sortedRoomsWithDistance
      .filter(room => !usedRoomNames.has(room.roomName)) // Exclude used room names
      .filter(room => room.roomName.toLowerCase().includes(searchTerm.toLowerCase())) // Apply search filter
  }, [sortedRoomsWithDistance, usedRoomNames, searchTerm])

  // Get close rooms for highlighting
  const closeRooms = useMemo(() => {
    return getCloseRooms(filteredRooms, 150) // Within 150px
  }, [filteredRooms])

  // Reset selection when filtered list changes
  useEffect(() => {
    setSelectedIndex(0)
  }, [searchTerm])

  // Focus appropriate input when dropdown becomes visible
  useEffect(() => {
    if (isVisible) {
      if (showCustomInput && customInputRef.current) {
        customInputRef.current.focus()
      } else if (searchInputRef.current) {
        searchInputRef.current.focus()
      }
    }
  }, [isVisible, showCustomInput])

  // Handle custom room name submission
  const handleCustomRoomSubmit = () => {
    if (customRoomName.trim()) {
      onSelectRoom(customRoomName.trim())
    }
  }

  // Toggle between search and custom input modes
  const toggleCustomInput = () => {
    setShowCustomInput(!showCustomInput)
    setSearchTerm('')
    setCustomRoomName('')
    setSelectedIndex(0)
  }

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isVisible) return

      switch (event.key) {
        case 'ArrowDown':
          if (!showCustomInput) {
            event.preventDefault()
            setSelectedIndex(prev =>
              prev < filteredRooms.length - 1 ? prev + 1 : prev
            )
          }
          break
        case 'ArrowUp':
          if (!showCustomInput) {
            event.preventDefault()
            setSelectedIndex(prev => prev > 0 ? prev - 1 : prev)
          }
          break
        case 'Enter':
          event.preventDefault()
          if (showCustomInput) {
            handleCustomRoomSubmit()
          } else if (filteredRooms.length > 0) {
            onSelectRoom(filteredRooms[selectedIndex].roomName)
          }
          break
        case 'Escape':
          event.preventDefault()
          onCancel()
          break
        case 'Tab':
          event.preventDefault()
          setShowDistanceInfo(!showDistanceInfo)
          break
        default:
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isVisible, showCustomInput, filteredRooms, selectedIndex, onSelectRoom, onCancel, showDistanceInfo])

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        onCancel()
      }
    }

    if (isVisible) {
      const timer = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside)
      }, 100)

      return () => {
        clearTimeout(timer)
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [isVisible, onCancel])

  if (!isVisible || roomNames.length === 0) {
    return null
  }

  return (
    <div
      ref={dropdownRef}
      className="room-name-dropdown distance-sorted"
      style={{
        position: 'fixed',
        left: position.x,
        top: position.y,
        zIndex: 9999
      }}
    >
      <div className="room-dropdown-header">
        <h4>
          Assign Room Name 
          {closeRooms.length > 0 && (
            <span className="close-rooms-indicator">
              ({closeRooms.length} nearby)
            </span>
          )}
        </h4>
        
        <div className="header-controls">
          <div className="input-mode-toggle">
            <button
              className={`mode-toggle-btn ${!showCustomInput ? 'active' : ''}`}
              onClick={() => !showCustomInput || toggleCustomInput()}
              title="Search existing rooms (sorted by distance)"
            >
              📋 Search
            </button>
            <button
              className={`mode-toggle-btn ${showCustomInput ? 'active' : ''}`}
              onClick={() => showCustomInput || toggleCustomInput()}
              title="Type custom room name"
            >
              ✏️ Custom
            </button>
          </div>
          
          {!showCustomInput && (
            <button
              className="distance-toggle-btn"
              onClick={() => setShowDistanceInfo(!showDistanceInfo)}
              title="Toggle distance information (Tab)"
            >
              {showDistanceInfo ? '📏' : '📏'}
            </button>
          )}
        </div>

        {showCustomInput ? (
          <input
            ref={customInputRef}
            type="text"
            placeholder="Type custom room name..."
            value={customRoomName}
            onChange={(e) => setCustomRoomName(e.target.value)}
            className="room-custom-input"
          />
        ) : (
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search room names (sorted by distance)..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="room-search-input"
          />
        )}
      </div>
      
      {showCustomInput ? (
        <div className="custom-room-preview">
          {customRoomName.trim() ? (
            <div className="custom-room-display">
              <span className="custom-room-label">Custom Room:</span>
              <span className="custom-room-name">"{customRoomName.trim()}"</span>
            </div>
          ) : (
            <div className="custom-room-hint">
              Type your custom room name above and press Enter or click Add
            </div>
          )}
        </div>
      ) : (
        <div className="room-dropdown-list">
          {filteredRooms.length > 0 ? (
            <>
              {closeRooms.length > 0 && (
                <div className="close-rooms-header">
                  <span className="section-label">🎯 Nearby Rooms</span>
                </div>
              )}
              {filteredRooms.map((room, index) => {
                const isClose = room.hasCoordinates && room.distance <= 150
                const isVeryClose = room.hasCoordinates && room.distance <= 50
                
                return (
                  <div
                    key={room.roomName}
                    className={`room-dropdown-item ${index === selectedIndex ? 'selected' : ''} ${isClose ? 'close-room' : ''} ${isVeryClose ? 'very-close-room' : ''}`}
                    onClick={() => onSelectRoom(room.roomName)}
                    onMouseEnter={() => setSelectedIndex(index)}
                  >
                    <div className="room-item-content">
                      <div className="room-name-section">
                        <span className="distance-indicator">
                          {getDistanceIndicator(room.distance, room.hasCoordinates)}
                        </span>
                        <span className="room-name">{room.roomName}</span>
                      </div>
                      {showDistanceInfo && (
                        <div className="distance-info">
                          <span className="distance-text">
                            {formatDistance(room.distance)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
              {closeRooms.length > 0 && filteredRooms.length > closeRooms.length && (
                <div className="other-rooms-header">
                  <span className="section-label">📍 Other Rooms</span>
                </div>
              )}
            </>
          ) : (
            <div className="room-dropdown-item no-results">
              No rooms found matching "{searchTerm}"
              <button
                className="switch-to-custom-btn"
                onClick={toggleCustomInput}
              >
                Type custom name instead
              </button>
            </div>
          )}
        </div>
      )}
      
      <div className="room-dropdown-footer">
        <button onClick={onCancel} className="cancel-button">
          Cancel (Esc)
        </button>

        {showCustomInput ? (
          <button
            onClick={handleCustomRoomSubmit}
            className="add-custom-button"
            disabled={!customRoomName.trim()}
            title={customRoomName.trim() ? `Add "${customRoomName.trim()}"` : 'Enter a room name first'}
          >
            Add Custom Room (Enter)
          </button>
        ) : (
          <>
            {filteredRooms.length > 0 && (
              <button
                onClick={() => onSelectRoom(filteredRooms[selectedIndex].roomName)}
                className="select-button"
              >
                Select (Enter)
              </button>
            )}
            <button
              onClick={toggleCustomInput}
              className="custom-mode-button"
              title="Switch to custom room name input"
            >
              ✏️ Custom Name
            </button>
          </>
        )}
      </div>
    </div>
  )
}

export default DistanceSortedRoomDropdown

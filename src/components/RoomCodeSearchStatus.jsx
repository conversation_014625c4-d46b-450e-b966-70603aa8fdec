const RoomCodeSearchStatus = ({
  searchStatus,
  isSearching,
  searchProgress,
  getSearchStats,
  startSearch,
  stopSearch,
  resetSearch,
  roomCodes,
  createAnnotationAtRoomCode,
  getFoundRoomCodes
}) => {
  const stats = getSearchStats()

  const getStatusIcon = () => {
    switch (searchStatus) {
      case 'searching':
        return '🔍'
      case 'completed':
        return '✅'
      case 'error':
        return '❌'
      default:
        return '⏳'
    }
  }

  const getStatusText = () => {
    switch (searchStatus) {
      case 'searching':
        return 'Searching for room codes...'
      case 'completed':
        return 'Room code search completed'
      case 'error':
        return 'Search failed'
      default:
        return 'Ready to search'
    }
  }

  const getProgressText = () => {
    if (isSearching && searchProgress.total > 0) {
      const percentage = Math.round((searchProgress.current / searchProgress.total) * 100)
      return `${searchProgress.current}/${searchProgress.total} pages (${percentage}%)`
    }
    return ''
  }

  if (!roomCodes || roomCodes.length === 0) {
    return null
  }

  return (
    <div className="room-code-search-status">
      <div className="search-header">
        <div className="search-status">
          <span className="status-icon">{getStatusIcon()}</span>
          <span className="status-text">{getStatusText()}</span>
        </div>
        
        {isSearching && (
          <button 
            onClick={stopSearch}
            className="stop-search-btn"
            title="Stop search"
          >
            ⏹️ Stop
          </button>
        )}
        
        {!isSearching && searchStatus !== 'searching' && (
          <div className="search-actions">
            <button 
              onClick={startSearch}
              className="start-search-btn"
              title="Start room code search"
            >
              🔍 Search
            </button>
            {searchStatus === 'completed' && (
              <button 
                onClick={resetSearch}
                className="reset-search-btn"
                title="Reset search results"
              >
                🔄 Reset
              </button>
            )}
          </div>
        )}
      </div>

      {isSearching && (
        <div className="search-progress">
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ 
                width: searchProgress.total > 0 
                  ? `${(searchProgress.current / searchProgress.total) * 100}%` 
                  : '0%' 
              }}
            />
          </div>
          <div className="progress-text">{getProgressText()}</div>
        </div>
      )}

      {searchStatus === 'completed' && (
        <div className="search-results">
          <div className="results-summary">
            <div className="stat-item">
              <span className="stat-label">Total Room Codes:</span>
              <span className="stat-value">{stats.totalCodes}</span>
            </div>
            <div className="stat-item found">
              <span className="stat-label">Found:</span>
              <span className="stat-value">{stats.foundCodes}</span>
            </div>
            <div className="stat-item not-found">
              <span className="stat-label">Not Found:</span>
              <span className="stat-value">{stats.notFoundCodes}</span>
            </div>
            <div className="stat-item percentage">
              <span className="stat-label">Success Rate:</span>
              <span className="stat-value">{stats.foundPercentage}%</span>
            </div>
          </div>

          {getFoundRoomCodes && stats.foundCodes > 0 && (
            <div className="found-codes-section">
              <h4>Found Room Codes (Click to create annotation):</h4>
              <div className="found-codes-list">
                {getFoundRoomCodes().slice(0, 10).map((codeData) => (
                  <button
                    key={codeData.code}
                    className="room-code-button"
                    onClick={() => {
                      if (createAnnotationAtRoomCode) {
                        // This would need to be connected to the annotation system
                        console.log(`Creating annotation for room code: ${codeData.code}`)
                        // createAnnotationAtRoomCode(codeData.code, annotationCreator)
                      }
                    }}
                    title={`${codeData.roomName} - Click to create annotation at (${Math.round(codeData.coordinates.x)}, ${Math.round(codeData.coordinates.y)})`}
                  >
                    {codeData.code}
                  </button>
                ))}
                {getFoundRoomCodes().length > 10 && (
                  <span className="more-codes">
                    +{getFoundRoomCodes().length - 10} more...
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {searchStatus === 'error' && (
        <div className="search-error">
          <p>Failed to search for room codes. Please try again.</p>
        </div>
      )}

      <style jsx>{`
        .room-code-search-status {
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 8px;
          padding: 16px;
          margin: 16px 0;
          font-size: 14px;
        }

        .search-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
        }

        .search-status {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .status-icon {
          font-size: 16px;
        }

        .status-text {
          font-weight: 500;
          color: #495057;
        }

        .search-actions {
          display: flex;
          gap: 8px;
        }

        .start-search-btn, .stop-search-btn, .reset-search-btn {
          padding: 6px 12px;
          border: 1px solid #ccc;
          border-radius: 4px;
          background: white;
          cursor: pointer;
          font-size: 12px;
          transition: all 0.2s;
        }

        .start-search-btn:hover {
          background: #e3f2fd;
          border-color: #2196f3;
        }

        .stop-search-btn:hover {
          background: #ffebee;
          border-color: #f44336;
        }

        .reset-search-btn:hover {
          background: #f3e5f5;
          border-color: #9c27b0;
        }

        .search-progress {
          margin-bottom: 12px;
        }

        .progress-bar {
          width: 100%;
          height: 8px;
          background: #e9ecef;
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: 4px;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #4caf50, #2196f3);
          transition: width 0.3s ease;
        }

        .progress-text {
          font-size: 12px;
          color: #6c757d;
          text-align: center;
        }

        .search-results {
          border-top: 1px solid #dee2e6;
          padding-top: 12px;
        }

        .results-summary {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 12px;
        }

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 8px;
          border-radius: 4px;
          background: white;
        }

        .stat-item.found {
          background: #e8f5e8;
        }

        .stat-item.not-found {
          background: #ffeaa7;
        }

        .stat-item.percentage {
          background: #e3f2fd;
        }

        .stat-label {
          font-size: 11px;
          color: #6c757d;
          margin-bottom: 2px;
        }

        .stat-value {
          font-size: 16px;
          font-weight: bold;
          color: #495057;
        }

        .search-error {
          color: #dc3545;
          background: #f8d7da;
          padding: 8px;
          border-radius: 4px;
          border: 1px solid #f5c6cb;
        }

        .found-codes-section {
          margin-top: 12px;
          padding-top: 12px;
          border-top: 1px solid #dee2e6;
        }

        .found-codes-section h4 {
          margin: 0 0 8px 0;
          font-size: 13px;
          color: #495057;
          font-weight: 600;
        }

        .found-codes-list {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
          max-height: 100px;
          overflow-y: auto;
        }

        .room-code-button {
          padding: 4px 8px;
          background: #e8f5e8;
          border: 1px solid #4caf50;
          border-radius: 4px;
          font-size: 11px;
          font-family: monospace;
          cursor: pointer;
          transition: all 0.2s;
          color: #2e7d32;
        }

        .room-code-button:hover {
          background: #4caf50;
          color: white;
          transform: translateY(-1px);
        }

        .more-codes {
          padding: 4px 8px;
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 4px;
          font-size: 11px;
          color: #6c757d;
          align-self: center;
        }
      `}</style>
    </div>
  )
}

export default RoomCodeSearchStatus

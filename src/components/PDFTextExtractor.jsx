import { useState, useCallback, useRef } from 'react'
import * as pdfjsLib from 'pdfjs-dist'

// Set up PDF.js worker - using local build files
pdfjsLib.GlobalWorkerOptions.workerSrc = '/PDF_BUILD/build/pdf.worker.mjs'

export const usePDFTextExtractor = (pdfToCanvasCoordinates) => {
  const [isSearching, setIsSearching] = useState(false)
  const [searchProgress, setSearchProgress] = useState({ current: 0, total: 0 })
  const [roomCodeCache, setRoomCodeCache] = useState(new Map()) // Map of room code -> coordinates
  const searchAbortController = useRef(null)

  // Extract text content and coordinates from a PDF page
  const extractPageTextContent = useCallback(async (page) => {
    try {
      const textContent = await page.getTextContent()
      const viewport = page.getViewport({ scale: 1 })
      
      const textItems = []
      
      textContent.items.forEach(item => {
        if (item.str && item.str.trim()) {
          // Transform coordinates from PDF coordinate system
          const transform = item.transform
          const x = transform[4]
          // Keep original PDF Y coordinate (bottom-left origin)
          // We'll handle the coordinate system conversion in pdfToCanvasCoordinates
          const y = transform[5]

          textItems.push({
            text: item.str.trim(),
            x: x,
            y: y,
            width: item.width,
            height: item.height,
            fontName: item.fontName,
            fontSize: transform[0] // Scale factor represents font size
          })
        }
      })
      
      return textItems
    } catch (error) {
      console.error('Error extracting text content from page:', error)
      return []
    }
  }, [])

  // Search for room codes in text items
  const searchRoomCodesInTextItems = useCallback((textItems, roomCodes, pdfIndex, pageIndex) => {
    const foundCodes = new Map()

    roomCodes.forEach(({ code, roomName }) => {
      // Search for exact matches of the room code with strict matching
      const matchingItems = textItems.filter(item => {
        const text = item.text.trim()

        // Exact match - the text is exactly the room code
        if (text === code) {
          return true
        }

        // Word boundary match - room code appears as a complete word
        // Use regex that ensures the code is not part of a longer alphanumeric sequence
        const escapedCode = code.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
        // Match the code when it's preceded and followed by non-alphanumeric characters or string boundaries
        const strictBoundaryRegex = new RegExp(`(?:^|[^a-zA-Z0-9.])${escapedCode}(?:[^a-zA-Z0-9.]|$)`, 'g')

        return strictBoundaryRegex.test(text)
      })

      if (matchingItems.length > 0) {
        // Store all matches for this room code, not just the first one
        const allMatches = matchingItems.map(match => {
          // Convert PDF coordinates to canvas coordinates for caching
          let canvasCoordinates = { x: match.x, y: match.y }
          let canvasWidth = match.width
          let canvasHeight = match.height

          if (pdfToCanvasCoordinates) {
            canvasCoordinates = pdfToCanvasCoordinates(match.x, match.y, pdfIndex, pageIndex)
            // Convert width and height by calculating the scale factor
            const bottomRight = pdfToCanvasCoordinates(match.x + match.width, match.y + match.height, pdfIndex, pageIndex)
            canvasWidth = bottomRight.x - canvasCoordinates.x
            canvasHeight = bottomRight.y - canvasCoordinates.y
          }

          return {
            coordinates: {
              // Store canvas coordinates for direct use in annotations
              x: canvasCoordinates.x,
              y: canvasCoordinates.y,
              width: canvasWidth,
              height: canvasHeight
            },
            // Also store original PDF coordinates for reference
            pdfCoordinates: {
              x: match.x,
              y: match.y,
              width: match.width,
              height: match.height
            },
            textItem: match,
            pdfIndex: pdfIndex,
            pageIndex: pageIndex
          }
        })

        foundCodes.set(code, {
          code: code,
          roomName: roomName,
          // Store the first match coordinates for backward compatibility
          coordinates: allMatches[0].coordinates,
          pdfCoordinates: allMatches[0].pdfCoordinates,
          textItem: allMatches[0].textItem,
          // Store all matches for advanced distance calculations
          allMatches: allMatches,
          matchCount: allMatches.length,
          found: true
        })

        console.log(`Found room code "${code}" with ${allMatches.length} matches. Primary location: PDF coordinates (${allMatches[0].pdfCoordinates.x}, ${allMatches[0].pdfCoordinates.y}) -> Canvas coordinates (${allMatches[0].coordinates.x}, ${allMatches[0].coordinates.y})`)
      }
    })

    return foundCodes
  }, [pdfToCanvasCoordinates])

  // Search for room codes across pages of provided PDFs (can be all PDFs or just current PDF)
  const searchRoomCodesInPDFs = useCallback(async (pdfDataArray, roomCodes, onProgress, basePdfIndex = 0) => {
    if (!pdfDataArray || pdfDataArray.length === 0 || !roomCodes || roomCodes.length === 0) {
      console.log('No PDFs or room codes to search')
      return new Map()
    }

    setIsSearching(true)
    searchAbortController.current = new AbortController()

    const allFoundCodes = new Map()
    let totalPages = 0
    let processedPages = 0

    // Calculate total pages across provided PDFs
    pdfDataArray.forEach(pdfData => {
      totalPages += pdfData.pages.length
    })

    setSearchProgress({ current: 0, total: totalPages })

    try {
      for (let arrayIndex = 0; arrayIndex < pdfDataArray.length; arrayIndex++) {
        const pdfData = pdfDataArray[arrayIndex]
        const actualPdfIndex = basePdfIndex + arrayIndex // Use actual PDF index for coordinate conversion
        console.log(`Searching in PDF: ${pdfData.name} (index: ${actualPdfIndex})`)

        // Load the PDF document for text extraction
        const arrayBuffer = await pdfData.file.arrayBuffer()
        const pdf = await pdfjsLib.getDocument(arrayBuffer).promise

        for (let pageIndex = 0; pageIndex < pdfData.pages.length; pageIndex++) {
          if (searchAbortController.current?.signal.aborted) {
            console.log('Search aborted by user')
            return allFoundCodes
          }

          try {
            const page = await pdf.getPage(pageIndex + 1)
            const textItems = await extractPageTextContent(page)

            // Search for room codes in this page's text
            const foundInPage = searchRoomCodesInTextItems(textItems, roomCodes, actualPdfIndex, pageIndex)

            // Add found codes to the overall results, merging multiple matches
            foundInPage.forEach((codeData, code) => {
              if (!allFoundCodes.has(code)) {
                // First occurrence of this room code
                allFoundCodes.set(code, {
                  ...codeData,
                  pdfIndex: actualPdfIndex,
                  pdfName: pdfData.name,
                  // Update all matches to include PDF metadata
                  allMatches: codeData.allMatches.map(match => ({
                    ...match,
                    pdfIndex: actualPdfIndex,
                    pdfName: pdfData.name
                  }))
                })
              } else {
                // Room code already found on another page - merge the matches
                const existingData = allFoundCodes.get(code)
                const newMatches = codeData.allMatches.map(match => ({
                  ...match,
                  pdfIndex: actualPdfIndex,
                  pdfName: pdfData.name
                }))

                // Combine all matches from different pages
                const combinedMatches = [...existingData.allMatches, ...newMatches]

                allFoundCodes.set(code, {
                  ...existingData,
                  // Update primary coordinates to the first match (for backward compatibility)
                  coordinates: combinedMatches[0].coordinates,
                  pdfCoordinates: combinedMatches[0].pdfCoordinates,
                  textItem: combinedMatches[0].textItem,
                  pdfIndex: combinedMatches[0].pdfIndex,
                  pageIndex: combinedMatches[0].pageIndex,
                  // Store all matches across all pages
                  allMatches: combinedMatches,
                  matchCount: combinedMatches.length
                })

                console.log(`Merged room code "${code}" - now has ${combinedMatches.length} total matches across pages`)
              }
            })
            
            processedPages++
            setSearchProgress({ current: processedPages, total: totalPages })
            
            // Call progress callback if provided
            if (onProgress) {
              onProgress(processedPages, totalPages, foundInPage.size)
            }
            
            // Small delay to prevent blocking the UI
            await new Promise(resolve => setTimeout(resolve, 10))
            
          } catch (pageError) {
            console.error(`Error processing page ${pageIndex + 1} of ${pdfData.name}:`, pageError)
            processedPages++
            setSearchProgress({ current: processedPages, total: totalPages })
          }
        }
      }
      
      console.log(`Search completed. Found ${allFoundCodes.size} room codes out of ${roomCodes.length}`)
      setRoomCodeCache(allFoundCodes)
      
    } catch (error) {
      console.error('Error during room code search:', error)
    } finally {
      setIsSearching(false)
      searchAbortController.current = null
    }
    
    return allFoundCodes
  }, [extractPageTextContent, searchRoomCodesInTextItems])

  // Abort ongoing search
  const abortSearch = useCallback(() => {
    if (searchAbortController.current) {
      searchAbortController.current.abort()
      setIsSearching(false)
    }
  }, [])

  // Get cached coordinates for a room code
  const getRoomCodeCoordinates = useCallback((roomCode) => {
    return roomCodeCache.get(roomCode) || null
  }, [roomCodeCache])

  // Clear the cache
  const clearCache = useCallback(() => {
    setRoomCodeCache(new Map())
  }, [])

  return {
    // State
    isSearching,
    searchProgress,
    roomCodeCache,
    
    // Actions
    searchRoomCodesInPDFs,
    abortSearch,
    getRoomCodeCoordinates,
    clearCache,
    extractPageTextContent,
    searchRoomCodesInTextItems
  }
}

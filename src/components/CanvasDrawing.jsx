import { useCallback } from 'react'

export const useCanvasDrawing = (canvasRef, baseImages, currentPageIndex, pdfPages) => {
  
  // Helper function to check if mouse is near first point
  const isMouseNearFirstPoint = useCallback((mousePos, polygonPoints, threshold = 15) => {
    if (!mousePos || !polygonPoints || polygonPoints.length === 0) return false

    const firstPoint = polygonPoints[0]
    const distance = Math.sqrt(
      Math.pow(mousePos.x - firstPoint.x, 2) +
      Math.pow(mousePos.y - firstPoint.y, 2)
    )

    return distance <= threshold
  }, [])

  // Draw only annotations without reloading the base image
  const drawAnnotationsOnly = useCallback((annotations, selectedAnnotations, currentAnnotation, polygonPoints, rectangleStartPoint, drawingMode, isDragging, currentMousePos, isRoomAssignmentActive = false, pendingAnnotation = null) => {
    const canvas = canvasRef.current
    if (!canvas || pdfPages.length === 0) return

    const ctx = canvas.getContext('2d')
    const currentPage = pdfPages[currentPageIndex]
    const baseImg = baseImages.current[currentPageIndex]

    // Set canvas size if needed
    if (canvas.width !== currentPage.width || canvas.height !== currentPage.height) {
      canvas.width = currentPage.width
      canvas.height = currentPage.height
    }

    // Clear canvas first
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Draw base image if available, otherwise draw from page data
    if (baseImg) {
      ctx.drawImage(baseImg, 0, 0)
    } else {
      // Fallback: create image from page data
      const img = new Image()
      img.onload = () => {
        ctx.drawImage(img, 0, 0)
        baseImages.current[currentPageIndex] = img
        // Redraw annotations after base image is loaded
        drawAnnotationsOnly(annotations, selectedAnnotations, currentAnnotation, polygonPoints, rectangleStartPoint, drawingMode)
      }
      img.src = currentPage.imageData
      return // Exit early, will be called again after image loads
    }

    // Draw existing annotations
    annotations.forEach(annotation => {
      ctx.save() // Save current context state

      const isSelected = selectedAnnotations && selectedAnnotations.some(selected => selected.id === annotation.id)
      const isPending = pendingAnnotation && annotation.id === pendingAnnotation.id

      // Special highlighting for pending annotation during room assignment
      if (isPending && isRoomAssignmentActive) {
        ctx.strokeStyle = '#ff6b00'  // Orange for pending annotation
        ctx.lineWidth = 6  // Extra thick for pending
        ctx.setLineDash([15, 5])  // Distinctive dash pattern
        ctx.globalAlpha = 1.0
      } else if (isSelected) {
        ctx.strokeStyle = '#00ff00'  // Green for selected
        ctx.lineWidth = 5
        ctx.setLineDash([10, 5])
        ctx.globalAlpha = 1.0
      } else {
        ctx.strokeStyle = '#ff0000'  // Red for normal
        ctx.lineWidth = 4
        ctx.setLineDash([])
        ctx.globalAlpha = 1.0
      }

      ctx.lineCap = 'round'
      ctx.lineJoin = 'round'

      if (annotation.type === 'rectangle') {
        // Draw rectangle with thick, visible stroke
        ctx.strokeRect(annotation.x, annotation.y, annotation.width, annotation.height)

        // Add subtle highlight for all annotations, stronger for selected/pending
        if (isPending && isRoomAssignmentActive) {
          ctx.fillStyle = 'rgba(255, 107, 0, 0.25)'  // Orange highlight for pending
          ctx.fillRect(annotation.x, annotation.y, annotation.width, annotation.height)
        } else if (isSelected) {
          ctx.fillStyle = 'rgba(0, 255, 0, 0.15)'  // Green highlight for selected
          ctx.fillRect(annotation.x, annotation.y, annotation.width, annotation.height)
        } else {
          ctx.fillStyle = 'rgba(255, 0, 0, 0.08)'  // Red highlight for normal
          ctx.fillRect(annotation.x, annotation.y, annotation.width, annotation.height)
        }

        // Draw selection handles for selected rectangle (only when single selection)
        if (isSelected && selectedAnnotations && selectedAnnotations.length === 1) {
          const handleSize = 10
          const { x, y, width, height } = annotation

          // Draw corner handles
          ctx.fillStyle = '#00ff00'
          ctx.strokeStyle = '#ffffff'
          ctx.lineWidth = 2

          const corners = [
            { x: x - handleSize/2, y: y - handleSize/2 }, // northwest
            { x: x + width - handleSize/2, y: y - handleSize/2 }, // northeast
            { x: x - handleSize/2, y: y + height - handleSize/2 }, // southwest
            { x: x + width - handleSize/2, y: y + height - handleSize/2 } // southeast
          ]

          corners.forEach(corner => {
            ctx.fillRect(corner.x, corner.y, handleSize, handleSize)
            ctx.strokeRect(corner.x, corner.y, handleSize, handleSize)
          })

          // Draw edge handles
          ctx.fillStyle = '#00aa00' // Slightly darker green for edge handles
          const edgeHandleSize = 8

          const edges = [
            { x: x + width/2 - edgeHandleSize/2, y: y - edgeHandleSize/2 }, // north
            { x: x + width/2 - edgeHandleSize/2, y: y + height - edgeHandleSize/2 }, // south
            { x: x - edgeHandleSize/2, y: y + height/2 - edgeHandleSize/2 }, // west
            { x: x + width - edgeHandleSize/2, y: y + height/2 - edgeHandleSize/2 } // east
          ]

          edges.forEach(edge => {
            ctx.fillRect(edge.x, edge.y, edgeHandleSize, edgeHandleSize)
            ctx.strokeRect(edge.x, edge.y, edgeHandleSize, edgeHandleSize)
          })
        }

        // Draw room name text in top-left corner if available
        const roomName = annotation.roomName || annotation.label
        if (roomName && roomName !== `Rectangle ${annotation.id}` && !roomName.startsWith('Rectangle ')) {
          ctx.fillStyle = isSelected ? '#00ff00' : '#ff0000'
          ctx.font = 'bold 18px Arial'  // Increased from 14px to 18px

          // Create background for text
          const textMetrics = ctx.measureText(roomName)
          const textWidth = textMetrics.width
          const textHeight = 20  // Increased from 16 to 20
          const padding = 6  // Increased padding from 4 to 6

          // Position text in top-left corner with some padding
          const textX = annotation.x + padding
          const textY = annotation.y + padding + textHeight - 4

          // Draw background rectangle for text with rounded corners effect
          ctx.fillStyle = isSelected ? 'rgba(0, 255, 0, 0.9)' : 'rgba(255, 0, 0, 0.9)'
          ctx.fillRect(textX - 4, textY - textHeight + 2, textWidth + 8, textHeight + 4)

          // Draw text
          ctx.fillStyle = 'white'
          ctx.fillText(roomName, textX, textY)
        }
      } else if (annotation.type === 'polygon' && annotation.points.length > 1) {
        // Draw polygon with thick, visible stroke
        ctx.beginPath()
        ctx.moveTo(annotation.points[0].x, annotation.points[0].y)
        for (let i = 1; i < annotation.points.length; i++) {
          ctx.lineTo(annotation.points[i].x, annotation.points[i].y)
        }
        ctx.closePath()
        ctx.stroke()

        // Add subtle highlight for all polygons, stronger for selected
        if (isSelected) {
          ctx.fillStyle = 'rgba(0, 255, 0, 0.15)'
          ctx.fill()
        } else {
          ctx.fillStyle = 'rgba(255, 0, 0, 0.08)'
          ctx.fill()
        }

        // Draw selection handles for selected polygon vertices (only when single selection)
        if (isSelected && selectedAnnotations && selectedAnnotations.length === 1) {
          const handleSize = 10
          ctx.fillStyle = '#00ff00'
          ctx.strokeStyle = '#ffffff'
          ctx.lineWidth = 2

          annotation.points.forEach(point => {
            // Draw vertex handle with border for better visibility
            ctx.fillRect(point.x - handleSize/2, point.y - handleSize/2, handleSize, handleSize)
            ctx.strokeRect(point.x - handleSize/2, point.y - handleSize/2, handleSize, handleSize)
          })
        }

        // Draw room name text near the first point if available
        const roomName = annotation.roomName || annotation.label
        if (roomName && roomName !== `Polygon ${annotation.id}` && !roomName.startsWith('Polygon ') && annotation.points.length > 0) {
          ctx.fillStyle = isSelected ? '#00ff00' : '#ff0000'
          ctx.font = 'bold 18px Arial'  // Increased from 14px to 18px

          // Create background for text
          const textMetrics = ctx.measureText(roomName)
          const textWidth = textMetrics.width
          const textHeight = 20  // Increased from 16 to 20
          const padding = 6  // Increased padding from 4 to 6

          // Position text near the first point with some offset
          const firstPoint = annotation.points[0]
          const textX = firstPoint.x + padding
          const textY = firstPoint.y - padding

          // Draw background rectangle for text with rounded corners effect
          ctx.fillStyle = isSelected ? 'rgba(0, 255, 0, 0.9)' : 'rgba(255, 0, 0, 0.9)'
          ctx.fillRect(textX - 4, textY - textHeight + 2, textWidth + 8, textHeight + 4)

          // Draw text
          ctx.fillStyle = 'white'
          ctx.fillText(roomName, textX, textY)
        }
      }

      ctx.restore() // Restore context state
    })

    // Draw current annotation being created
    if (currentAnnotation) {
      ctx.save()
      ctx.strokeStyle = '#0000ff'
      ctx.lineWidth = 4  // Even thicker line for better visibility
      ctx.setLineDash([10, 5])  // More visible dash pattern
      ctx.globalAlpha = 1.0
      ctx.lineCap = 'round'
      ctx.lineJoin = 'round'

      if (currentAnnotation.type === 'rectangle') {
        ctx.strokeRect(currentAnnotation.x, currentAnnotation.y, currentAnnotation.width, currentAnnotation.height)
      } else if (currentAnnotation.type === 'polygon' && polygonPoints.length > 0) {
        ctx.beginPath()
        ctx.moveTo(polygonPoints[0].x, polygonPoints[0].y)
        for (let i = 1; i < polygonPoints.length; i++) {
          ctx.lineTo(polygonPoints[i].x, polygonPoints[i].y)
        }
        ctx.stroke()

        // Draw points for current polygon
        const pointSize = 8  // Larger points for better visibility
        const isHoveringFirstPoint = polygonPoints.length >= 3 && isMouseNearFirstPoint(currentMousePos, polygonPoints)

        polygonPoints.forEach((point, index) => {
          // First point is green (different color), others are blue
          ctx.fillStyle = index === 0 ? '#00ff00' : '#0000ff'

          // Make first point larger and more prominent when hovering and polygon can be closed
          const currentPointSize = (index === 0 && isHoveringFirstPoint) ? pointSize + 4 : pointSize
          ctx.fillRect(point.x - currentPointSize/2, point.y - currentPointSize/2, currentPointSize, currentPointSize)

          // Add a white border to make the first point more visible
          if (index === 0) {
            ctx.strokeStyle = isHoveringFirstPoint ? '#ffff00' : '#ffffff' // Yellow when hovering
            ctx.lineWidth = isHoveringFirstPoint ? 3 : 2 // Thicker when hovering
            ctx.strokeRect(point.x - currentPointSize/2, point.y - currentPointSize/2, currentPointSize, currentPointSize)

            // Show "Click to close" text when hovering over first point
            if (isHoveringFirstPoint) {
              ctx.save()
              ctx.fillStyle = '#ffff00'
              ctx.font = 'bold 14px Arial'
              ctx.strokeStyle = '#000000'
              ctx.lineWidth = 3

              const text = 'Click to close polygon'
              const textMetrics = ctx.measureText(text)
              const textX = point.x - textMetrics.width / 2
              const textY = point.y - 20

              // Draw text with black outline for better visibility
              ctx.strokeText(text, textX, textY)
              ctx.fillText(text, textX, textY)
              ctx.restore()
            }
          }
        })
      }
      ctx.restore()
    }

    // Draw rectangle preview during dragging
    if (rectangleStartPoint && drawingMode === 'rectangle' && isDragging && currentMousePos) {
      ctx.save()
      ctx.strokeStyle = '#0000ff'
      ctx.fillStyle = 'rgba(0, 0, 255, 0.1)'
      ctx.lineWidth = 2
      ctx.setLineDash([5, 5])

      const startX = Math.min(rectangleStartPoint.x, currentMousePos.x)
      const startY = Math.min(rectangleStartPoint.y, currentMousePos.y)
      const width = Math.abs(currentMousePos.x - rectangleStartPoint.x)
      const height = Math.abs(currentMousePos.y - rectangleStartPoint.y)

      // Draw preview rectangle
      ctx.fillRect(startX, startY, width, height)
      ctx.strokeRect(startX, startY, width, height)

      ctx.restore()
    } else if (rectangleStartPoint && drawingMode === 'rectangle' && !isDragging) {
      // Draw start point when not dragging
      ctx.save()
      ctx.fillStyle = '#0000ff'
      ctx.globalAlpha = 1.0
      const pointSize = 10
      ctx.fillRect(rectangleStartPoint.x - pointSize/2, rectangleStartPoint.y - pointSize/2, pointSize, pointSize)

      // Draw crosshair to show exact coordinate
      ctx.strokeStyle = '#0000ff'
      ctx.lineWidth = 2
      ctx.beginPath()
      ctx.moveTo(rectangleStartPoint.x - 20, rectangleStartPoint.y)
      ctx.lineTo(rectangleStartPoint.x + 20, rectangleStartPoint.y)
      ctx.moveTo(rectangleStartPoint.x, rectangleStartPoint.y - 20)
      ctx.lineTo(rectangleStartPoint.x, rectangleStartPoint.y + 20)
      ctx.stroke()

      ctx.restore()
    }
  }, [canvasRef, baseImages, currentPageIndex, pdfPages, isMouseNearFirstPoint])

  return {
    drawAnnotationsOnly
  }
}

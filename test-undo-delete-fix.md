# Undo/Delete Issue Fix

## Problem
When deleting the last annotation, the second-to-last annotation also gets deleted when using undo.

## Root Cause Analysis
The issue was in the `useUndoRedo` hook implementation. The problem was with stale closures in the `pushToHistory`, `undo`, and `redo` callbacks:

1. **Stale Closure Issue**: The callbacks were capturing stale values of `currentIndex` and `history`
2. **Race Conditions**: Multiple state updates happening simultaneously caused inconsistent state
3. **Dependency Array Issues**: The dependency arrays included state that was being modified inside the callbacks

## Root Cause (Discovered through debugging)
The actual issue was in the **delete functions**. When deleting an annotation, the wrong state was being saved to history:

1. `deleteAnnotation()` called `updateCurrentAnnotations(filteredArray)`
2. `updateCurrentAnnotations()` saved the current state to history
3. But the "current state" being saved was stale or incorrect
4. When undo was called, it restored the wrong state

## Solution
Fixed the delete functions to explicitly save the correct state before deletion:

### Before (Problematic):
```javascript
const deleteAnnotation = useCallback((annotationId) => {
  const currentAnnotations = getCurrentAnnotations()
  const updatedAnnotations = currentAnnotations.filter(ann => ann.id !== annotationId)
  updateCurrentAnnotations(updatedAnnotations) // ❌ Saves wrong state to history
}, [getCurrentAnnotations, updateCurrentAnnotations])
```

### After (Fixed):
```javascript
const deleteAnnotation = useCallback((annotationId) => {
  const currentAnnotations = getCurrentAnnotations()

  // ✅ Explicitly save current state BEFORE deletion
  const currentState = {
    annotations: { ...annotations },
    pdfIndex: currentPdfIndex,
    pageIndex: currentPageIndex,
    timestamp: Date.now()
  }
  pushToHistory(currentState)

  const updatedAnnotations = currentAnnotations.filter(ann => ann.id !== annotationId)
  updateCurrentAnnotations(updatedAnnotations, true) // ✅ Skip history (already saved)
}, [getCurrentAnnotations, updateCurrentAnnotations, annotations, pushToHistory])
```

## Key Changes

1. **Fixed Delete Functions**: Modified `deleteAnnotation` and `deleteSelectedAnnotations` to explicitly save state before deletion
2. **Explicit History Management**: Save current state to history before making changes, then update with `skipHistory = true`
3. **Correct State Capture**: Ensure the state being saved contains the annotations that existed before deletion
4. **Simplified History Logic**: Removed complex initialization logic that was causing duplicate entries

## Testing Steps

1. **Load a PDF file**
2. **Create first annotation** - verify undo button appears
3. **Create second annotation** - verify both annotations exist
4. **Delete the second annotation** - verify only first annotation remains
5. **Press undo** - verify second annotation is restored (both should be visible)
6. **Press undo again** - verify first annotation is also restored (both should still be visible)
7. **Press undo again** - verify both annotations are removed (empty state)

## Expected Behavior After Fix

- ✅ Undo button appears after first annotation
- ✅ Deleting last annotation only removes that annotation
- ✅ Undo after deletion properly restores the deleted annotation
- ✅ Multiple undos work correctly in sequence
- ✅ No unexpected annotation disappearances

## Files Modified

- `src/components/AnnotationsHandler.jsx`: Fixed `useUndoRedo` hook implementation

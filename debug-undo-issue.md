# Debug: Undo Issue Analysis

## Problem Description
When deleting the last annotation, the second-to-last annotation also gets deleted.

## Hypothesis
The issue might be related to how the history is being managed during deletions. Let's trace through what happens:

### Scenario: Create 2 boxes, then delete the last one

1. **Initial state**: No annotations
   - `annotations = {}`
   - `history = []`
   - `currentIndex = -1`

2. **Create first box**:
   - Initialize history with empty state: `history = [emptyState]`, `currentIndex = 0`
   - Save current state before adding: `history = [emptyState, stateWith0Boxes]`, `currentIndex = 1`
   - Add first box: `annotations = {"0-0": [box1]}`

3. **Create second box**:
   - Save current state before adding: `history = [emptyState, stateWith0Boxes, stateWith1Box]`, `currentIndex = 2`
   - Add second box: `annotations = {"0-0": [box1, box2]}`

4. **Delete last box (box2)**:
   - Save current state before deleting: `history = [emptyState, stateWith0Boxes, stateWith1Box, stateWith2Boxes]`, `currentIndex = 3`
   - Delete box2: `annotations = {"0-0": [box1]}`

5. **Undo after deletion**:
   - Should restore: `stateWith2Boxes` (which has both boxes)
   - But if there's a bug, it might restore `stateWith1Box` or `stateWith0Boxes`

## Potential Issues

1. **Double history push**: The initialization logic might be pushing an extra state
2. **Wrong state being saved**: The state being saved might not be the current state
3. **Index management**: The currentIndex might be getting corrupted

## Investigation Steps

1. Add console.log to track history changes
2. Verify what state is being saved before deletion
3. Check if the undo is restoring the correct state
